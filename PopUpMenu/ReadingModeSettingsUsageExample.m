//
//  ReadingModeSettingsUsageExample.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingModeSettingsUsageExample.h"
#import "ReadingModeSettingsController.h"
#import "ReadingModeBackgroundTheme.h"

@interface ReadingModeSettingsUsageExample () <ReadingModeSettingsDelegate>

@property (nonatomic, strong) UIButton *settingsButton;

@end

@implementation ReadingModeSettingsUsageExample

- (void)viewDidLoad {
    [super viewDidLoad];

    // 创建设置按钮（通常是右上角的"..."按钮）
    self.settingsButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.settingsButton setTitle:@"⋯" forState:UIControlStateNormal];
    [self.settingsButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.settingsButton.titleLabel.font = [UIFont systemFontOfSize:20];
    self.settingsButton.frame = CGRectMake(self.view.frame.size.width - 50, 50, 40, 40);
    [self.settingsButton addTarget:self action:@selector(showReadingModeSettings) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.settingsButton];
}

#pragma mark - 显示阅读模式设置弹窗

- (void)showReadingModeSettings {
    // 计算弹窗的源矩形（从设置按钮位置弹出）
    CGRect sourceRect = self.settingsButton.frame;

    // 显示阅读模式设置弹窗
    [ReadingModeSettingsController showAt:self
                               sourceRect:sourceRect
                                 delegate:self];
}

#pragma mark - ReadingModeSettingsDelegate

- (void)readingModeSettingsDidChangeFontSize:(NSInteger)fontSize {
    NSLog(@"字体大小已更改为: %ldpx", (long)fontSize);

    // 在这里实现字体大小更改的逻辑
    // 例如：更新网页内容的字体大小
    [self applyFontSize:fontSize];
}

- (void)readingModeSettingsDidChangeBackgroundTheme:(ReadingModeBackgroundTheme *)theme {
    NSLog(@"背景主题已更改为: %@ (类型: %ld)", theme.displayName, (long)theme.themeType);

    // 在这里实现背景主题更改的逻辑
    // 例如：更新网页内容的背景颜色
    [self applyBackgroundTheme:theme];
}

#pragma mark - 应用设置

- (void)applyFontSize:(NSInteger)fontSize {
    // 示例：通过JavaScript更改网页字体大小
    NSString *jsCode = [NSString stringWithFormat:@"document.body.style.fontSize = '%ldpx';", (long)fontSize];

    // 如果有WKWebView实例，可以这样执行：
    // [self.webView evaluateJavaScript:jsCode completionHandler:nil];

    // 或者更新其他UI元素的字体大小
    // [self updateUIFontSize:fontSize];
}

- (void)applyBackgroundTheme:(ReadingModeBackgroundTheme *)theme {
    // 示例：直接使用主题模型的颜色信息
    NSString *backgroundColor = theme.backgroundColorHex;
    NSString *textColor = theme.textColorHex;

    // 示例：通过JavaScript更改网页背景颜色和文字颜色
    NSString *jsCode = [NSString stringWithFormat:@"document.body.style.backgroundColor = '%@'; document.body.style.color = '%@';",
                       backgroundColor, textColor];

    // 如果有WKWebView实例，可以这样执行：
    // [self.webView evaluateJavaScript:jsCode completionHandler:nil];

    // 或者更新其他UI元素的背景颜色
    // [self updateUIBackgroundTheme:theme];

    // 如果是暗色主题，可以进行特殊处理
    if (theme.isDarkTheme) {
        // 暗色主题的特殊处理逻辑
        NSLog(@"应用暗色主题: %@", theme.displayName);
    }

    // 也可以根据具体的主题类型进行特殊处理
    switch (theme.themeType) {
        case ReadingModeThemeTypeDark:
            // 深色主题的特殊处理
            NSLog(@"应用深色主题特殊效果");
            break;
        case ReadingModeThemeTypeGreen:
            // 护眼绿主题的特殊处理
            NSLog(@"应用护眼绿主题特殊效果");
            break;
        case ReadingModeThemeTypeBlue:
            // 护眼蓝主题的特殊处理
            NSLog(@"应用护眼蓝主题特殊效果");
            break;
        default:
            // 其他主题的默认处理
            break;
    }
}

@end

/*
使用说明：

1. 在需要显示阅读模式设置的控制器中导入头文件：
   #import "ReadingModeSettingsController.h"

2. 让控制器遵循ReadingModeSettingsDelegate协议：
   @interface YourViewController : UIViewController <ReadingModeSettingsDelegate>

3. 在适当的位置（如菜单按钮点击事件）调用显示方法：
   [ReadingModeSettingsController showAt:self
                              sourceRect:buttonFrame
                                delegate:self];

4. 实现代理方法来处理设置变更：
   - (void)readingModeSettingsDidChangeFontSize:(NSInteger)fontSize;
   - (void)readingModeSettingsDidChangeBackgroundTheme:(ReadingModeBackgroundTheme *)theme;

5. 弹窗会自动保存用户设置到NSUserDefaults，下次启动时会自动加载。

特性：
- iOS原生UIPopoverPresentationController实现
- 完全还原HTML版本的UI样式（圆角、阴影、字体等）
- 支持暗黑模式自动适配
- 字体大小范围：12px-20px，支持+/-按钮控制
- 9种背景颜色主题选择
- 设置实时保存和加载
- 代理模式，便于集成到现有项目
- 使用枚举类型确保类型安全
- 支持字符串和枚举的双向转换
- 向后兼容性支持
- 内置"退出阅读模式"按钮，UI样式与HTML版本完全一致
- 无高亮效果，保持简洁的视觉风格
*/
