//
//  ReadingModeSettingsController.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingModeSettingsController.h"
#import "ReadingModeBackgroundTheme.h"

#import "UIColor+Helper.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "MaizyHeader.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

#import "PreferenceModel.h"
#import "PreferenceManager.h"
#import "BrowserUtils.h"
#import "PPNotifications.h"

#define kReaderFontSizeKey @"readingFontSize"
#define kReaderBackgroundTypeKey @"readingBackgroundType"
#define kReaderMinFont 5
#define kReaderMaxFont 50

@interface ReadingModeSettingsController ()

@property (nonatomic, strong) UIView *containerView;
//@property (nonatomic, strong) UILabel *titleLabel;

// 字体大小控制
@property (nonatomic, strong) UIView *fontSizeSection;
@property (nonatomic, strong) UIView *fontSizeHeaderView;
@property (nonatomic, strong) UILabel *fontSizeLabel;
@property (nonatomic, strong) UIButton *exitReadingButton;
@property (nonatomic, strong) UIView *fontSizeControlView;
@property (nonatomic, strong) UIButton *decreaseFontButton;
@property (nonatomic, strong) UILabel *fontSizeDisplayLabel;
@property (nonatomic, strong) UIButton *increaseFontButton;

// 背景颜色选择
@property (nonatomic, strong) UIView *backgroundSection;
@property (nonatomic, strong) UILabel *backgroundLabel;
@property (nonatomic, strong) UIView *backgroundOptionsView;
@property (nonatomic, strong) NSMutableArray<UIButton *> *backgroundButtons;

// 设置数据
@property (nonatomic, assign) NSInteger currentFontSize;
@property (nonatomic, strong) ReadingModeBackgroundTheme *currentBackgroundTheme;
@property (nonatomic, strong) NSArray<ReadingModeBackgroundTheme *> *backgroundThemes;

// 退出阅读模式按钮
@property (nonatomic, strong) UIButton *exitReaderButton;

@end

@implementation ReadingModeSettingsController

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self setupData];
    [self addSubviews];
    [self defineLayout];
    [self setupActions];
    [self loadSavedSettings];
    [self applyTheme];
}

// 导航栏样式
- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleNoneWithDefaultContent;
}

- (CGSize)preferredContentSize
{
    // 计算弹窗高度：字体大小区域 + 背景颜色区域 + 内边距
    CGFloat fontSectionHeight = 20 + 12 + 52;
    CGFloat backgroundSectionHeight = 50*3 + 8*2;

    CGFloat totalHeight = 24 + fontSectionHeight + 12 + backgroundSectionHeight + 36;

    return CGSizeMake(280.0, totalHeight);
}

+ (void)showAt:(UIViewController *)controller sourceRect:(CGRect)sourceRect delegate:(id<ReadingModeSettingsDelegate>)delegate
{
    ReadingModeSettingsController* vc = [ReadingModeSettingsController new];
    vc.delegate = delegate;
    vc.modalPresentationStyle = UIModalPresentationPopover;
    vc.popoverPresentationController.sourceView = controller.view;
    vc.popoverPresentationController.sourceRect = sourceRect;
    vc.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionUp;

    vc.popoverPresentationController.delegate = vc;

    [controller presentViewController:vc animated:YES completion:nil];
}

#pragma mark -- 数据初始化
- (void)setupData
{
    // 字体大小范围：12-20px，默认14px
    self.currentFontSize = 14;

    // 获取所有默认背景主题
    self.backgroundThemes = [ReadingModeBackgroundTheme defaultBackgroundThemes];

    // 设置默认主题
    self.currentBackgroundTheme = [ReadingModeBackgroundTheme defaultTheme];

    self.backgroundButtons = [NSMutableArray array];
}

#pragma mark -- 夜间模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        self.containerView.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
//        self.titleLabel.textColor = [UIColor whiteColor];
        self.fontSizeLabel.textColor = [UIColor whiteColor];
        self.backgroundLabel.textColor = [UIColor whiteColor];
        self.fontSizeDisplayLabel.textColor = [UIColor whiteColor];

        // 退出按钮暗黑模式
        self.exitReadingButton.layer.borderColor = [UIColor colorWithHexString:@"#444444"].CGColor;
        self.exitReadingButton.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
        [self.exitReadingButton setTitleColor:[UIColor colorWithHexString:@"#cccccc"] forState:UIControlStateNormal];
        self.exitReadingButton.tintColor = [UIColor colorWithHexString:@"#cccccc"];
    } else {
        self.view.backgroundColor = [UIColor whiteColor];
        self.containerView.backgroundColor = [UIColor whiteColor];
//        self.titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.fontSizeLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.backgroundLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        self.fontSizeDisplayLabel.textColor = [UIColor colorWithHexString:@"#333333"];

        // 退出按钮浅色模式
        self.exitReadingButton.layer.borderColor = [UIColor colorWithHexString:@"#e8e8e8"].CGColor;
        self.exitReadingButton.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];
        [self.exitReadingButton setTitleColor:[UIColor colorWithHexString:@"#666666"] forState:UIControlStateNormal];
        self.exitReadingButton.tintColor = [UIColor colorWithHexString:@"#666666"];
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    if(needReload) {
        [self applyTheme];
    }
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    [self applyTheme];
}

#pragma mark -- UIPopoverPresentationControllerDelegate

- (UIModalPresentationStyle)adaptivePresentationStyleForPresentationController:(UIPresentationController *)controller
{
    return UIModalPresentationNone;
}

#pragma mark -- 设置加载和保存
- (void)loadSavedSettings
{
    // 从本地存储加载设置
    NSInteger savedFontSize = [[NSUserDefaults standardUserDefaults] integerForKey:kReaderFontSizeKey];
    if (savedFontSize >= kReaderMinFont && savedFontSize <= kReaderMaxFont) {
        self.currentFontSize = savedFontSize;
    }

    // 使用枚举类型加载
    NSInteger savedBackgroundType = [[NSUserDefaults standardUserDefaults] integerForKey:kReaderBackgroundTypeKey];
    if (savedBackgroundType >= 0 && savedBackgroundType < ReadingModeThemeTypeCount) {
        self.currentBackgroundTheme = [ReadingModeBackgroundTheme themeForType:(ReadingModeThemeType)savedBackgroundType];
    }

    [self updateFontSizeDisplay];
    [self updateBackgroundSelection];
}

- (void)saveSettings
{
    [[NSUserDefaults standardUserDefaults] setInteger:self.currentFontSize forKey:kReaderFontSizeKey];
    [[NSUserDefaults standardUserDefaults] setInteger:self.currentBackgroundTheme.themeType forKey:kReaderBackgroundTypeKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

#pragma mark -- UI更新
- (void)updateFontSizeDisplay
{
    self.fontSizeDisplayLabel.text = [NSString stringWithFormat:@"%ldpx", (long)self.currentFontSize];

    // 更新按钮状态
    self.decreaseFontButton.enabled = (self.currentFontSize > kReaderMinFont);
    self.increaseFontButton.enabled = (self.currentFontSize < kReaderMaxFont);

    // 更新按钮样式
    [self updateFontButtonStyles];
}

- (void)updateFontButtonStyles
{
    // 减号按钮样式
    if (self.decreaseFontButton.enabled) {
        self.decreaseFontButton.backgroundColor = [UIColor whiteColor];
        [self.decreaseFontButton setTitleColor:[UIColor colorWithHexString:@"#4285f4"] forState:UIControlStateNormal];
    } else {
        self.decreaseFontButton.backgroundColor = [UIColor colorWithHexString:@"#e8e8e8"];
        [self.decreaseFontButton setTitleColor:[UIColor colorWithHexString:@"#cccccc"] forState:UIControlStateNormal];
    }

    // 加号按钮样式
    if (self.increaseFontButton.enabled) {
        self.increaseFontButton.backgroundColor = [UIColor whiteColor];
        [self.increaseFontButton setTitleColor:[UIColor colorWithHexString:@"#4285f4"] forState:UIControlStateNormal];
    } else {
        self.increaseFontButton.backgroundColor = [UIColor colorWithHexString:@"#e8e8e8"];
        [self.increaseFontButton setTitleColor:[UIColor colorWithHexString:@"#cccccc"] forState:UIControlStateNormal];
    }
}

- (void)updateBackgroundSelection
{
    for (NSInteger i = 0; i < self.backgroundButtons.count; i++) {
        UIButton *button = self.backgroundButtons[i];
        ReadingModeBackgroundTheme *theme = self.backgroundThemes[i];

        BOOL isSelected = [theme isEqualToTheme:self.currentBackgroundTheme];

        if (isSelected) {
            button.layer.borderColor = [UIColor colorWithHexString:@"#4285f4"].CGColor;
            button.backgroundColor = [UIColor colorWithHexString:@"#f8f9ff"];
        } else {
            button.layer.borderColor = [UIColor colorWithHexString:@"#e8e8e8"].CGColor;
            button.backgroundColor = [UIColor whiteColor];
        }
    }
}

#pragma mark -- 事件处理
- (void)setupActions
{
    [self.decreaseFontButton addTarget:self action:@selector(decreaseFontSize) forControlEvents:UIControlEventTouchUpInside];
    [self.increaseFontButton addTarget:self action:@selector(increaseFontSize) forControlEvents:UIControlEventTouchUpInside];
    [self.exitReadingButton addTarget:self action:@selector(exitReadingMode) forControlEvents:UIControlEventTouchUpInside];
}

- (void)decreaseFontSize
{
    if (self.currentFontSize > kReaderMinFont) {
        self.currentFontSize--;
        [self updateFontSizeDisplay];
        [self saveSettings];

        if (self.delegate && [self.delegate respondsToSelector:@selector(readingModeSettingsDidChangeFontSize:)]) {
            [self.delegate readingModeSettingsDidChangeFontSize:self.currentFontSize];
        }
    }
}

- (void)increaseFontSize
{
    if (self.currentFontSize < kReaderMaxFont) {
        self.currentFontSize++;
        [self updateFontSizeDisplay];
        [self saveSettings];

        if (self.delegate && [self.delegate respondsToSelector:@selector(readingModeSettingsDidChangeFontSize:)]) {
            [self.delegate readingModeSettingsDidChangeFontSize:self.currentFontSize];
        }
    }
}

- (void)backgroundButtonTapped:(UIButton *)sender
{
    NSInteger index = [self.backgroundButtons indexOfObject:sender];
    if (index != NSNotFound && index < self.backgroundThemes.count) {
        ReadingModeBackgroundTheme *theme = self.backgroundThemes[index];
        self.currentBackgroundTheme = theme;

        [self updateBackgroundSelection];
        [self saveSettings];

        if (self.delegate && [self.delegate respondsToSelector:@selector(readingModeSettingsDidChangeBackgroundTheme:)]) {
            [self.delegate readingModeSettingsDidChangeBackgroundTheme:self.currentBackgroundTheme];
        }
    }
}

- (void)exitReadingMode
{
    // 关闭弹窗并退出阅读模式
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark -- 布局
- (void)addSubviews
{
    [self.view addSubview:self.containerView];
//    [self.containerView addSubview:self.titleLabel];
    [self.containerView addSubview:self.fontSizeSection];
    [self.containerView addSubview:self.backgroundSection];

    // 字体大小区域
    [self.fontSizeSection addSubview:self.fontSizeHeaderView];
    [self.fontSizeSection addSubview:self.fontSizeControlView];
    [self.fontSizeHeaderView addSubview:self.fontSizeLabel];
    [self.fontSizeHeaderView addSubview:self.exitReadingButton];
    [self.fontSizeControlView addSubview:self.decreaseFontButton];
    [self.fontSizeControlView addSubview:self.fontSizeDisplayLabel];
    [self.fontSizeControlView addSubview:self.increaseFontButton];

    // 背景颜色区域
    [self.backgroundSection addSubview:self.backgroundLabel];
    [self.backgroundSection addSubview:self.backgroundOptionsView];

    // 添加背景颜色按钮
    for (NSInteger i = 0; i < self.backgroundThemes.count; i++) {
        UIButton *button = [self createBackgroundButtonForIndex:i];
        [self.backgroundButtons addObject:button];
        [self.backgroundOptionsView addSubview:button];
    }
}

- (void)defineLayout
{
    // 容器视图
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];

    // 标题
//    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.containerView).offset(16);
//        make.left.equalTo(self.containerView).offset(20);
//        make.right.equalTo(self.containerView).offset(-20);
//        make.height.mas_equalTo(44);
//    }];

    // 字体大小区域
    [self.fontSizeSection mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.titleLabel.mas_bottom).offset(12);
        make.top.equalTo(self.containerView).offset(24);
        make.left.equalTo(self.containerView).offset(20);
        make.right.equalTo(self.containerView).offset(-20);
        make.bottom.equalTo(self.fontSizeControlView);
    }];

    [self.fontSizeHeaderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.fontSizeSection);
        make.height.mas_equalTo(28);
    }];

    [self.fontSizeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.centerY.equalTo(self.fontSizeHeaderView);
    }];

    [self.exitReadingButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.centerY.equalTo(self.fontSizeHeaderView);
    }];

    [self.fontSizeControlView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.fontSizeHeaderView.mas_bottom).offset(12);
        make.left.right.equalTo(self.fontSizeSection);
        make.height.mas_equalTo(52);
    }];

    [self.decreaseFontButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.fontSizeControlView).offset(12);
        make.centerY.equalTo(self.fontSizeControlView);
        make.width.height.mas_equalTo(36);
    }];

    [self.fontSizeDisplayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.fontSizeControlView);
        make.width.mas_equalTo(60);
    }];

    [self.increaseFontButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.fontSizeControlView).offset(-12);
        make.centerY.equalTo(self.fontSizeControlView);
        make.width.height.mas_equalTo(36);
    }];

    // 背景颜色区域
    [self.backgroundSection mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.fontSizeSection.mas_bottom).offset(12);
        make.left.equalTo(self.containerView).offset(20);
        make.right.equalTo(self.containerView).offset(-20);
        make.bottom.equalTo(self.containerView).offset(-16);
    }];

    [self.backgroundLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.backgroundSection);
        make.height.mas_equalTo(20);
    }];

    [self.backgroundOptionsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backgroundLabel.mas_bottom).offset(12);
        make.left.right.bottom.equalTo(self.backgroundSection);
    }];

    // 背景颜色按钮布局（3x3网格）
    for (NSInteger i = 0; i < self.backgroundButtons.count; i++) {
        UIButton *button = self.backgroundButtons[i];
        NSInteger row = i / 3;
        NSInteger col = i % 3;

        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.backgroundOptionsView).offset(row * 58);
            make.left.equalTo(self.backgroundOptionsView).offset(col * 80);
            make.width.mas_equalTo(72);
            make.height.mas_equalTo(50);
        }];
    }
}

#pragma mark -- UI创建
- (UIButton *)createBackgroundButtonForIndex:(NSInteger)index
{
    ReadingModeBackgroundTheme *theme = self.backgroundThemes[index];

    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.layer.cornerRadius = 10.0;
    button.layer.borderWidth = 2.0;
    button.layer.borderColor = [UIColor colorWithHexString:@"#e8e8e8"].CGColor;
    button.backgroundColor = [UIColor whiteColor];

    // 创建颜色预览视图
    UIView *colorPreview = [[UIView alloc] init];
    colorPreview.backgroundColor = theme.backgroundColor;
    colorPreview.layer.cornerRadius = 3.0;
    colorPreview.layer.borderWidth = 1.0;
    colorPreview.layer.borderColor = [UIColor colorWithHexString:@"#dddddd"].CGColor;
    colorPreview.userInteractionEnabled = NO;
    [button addSubview:colorPreview];

    // 创建标题标签
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = theme.displayName;
    titleLabel.font = [UIFont systemFontOfSize:10.0 weight:UIFontWeightMedium];
    titleLabel.textColor = [UIColor colorWithHexString:@"#666666"];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.userInteractionEnabled = NO;
    [button addSubview:titleLabel];

    // 布局
    [colorPreview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(button);
        make.top.equalTo(button).offset(12);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(12);
    }];

    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(button);
        make.top.equalTo(colorPreview.mas_bottom).offset(4);
        make.left.right.equalTo(button);
    }];

    [button addTarget:self action:@selector(backgroundButtonTapped:) forControlEvents:UIControlEventTouchUpInside];

    return button;
}

#pragma mark -- Getters

- (UIView *)containerView
{
    if (!_containerView) {
        _containerView = [[UIView alloc] init];
        _containerView.backgroundColor = [UIColor whiteColor];
        _containerView.layer.cornerRadius = 16.0;
        _containerView.layer.masksToBounds = YES;

        // 添加阴影
        _containerView.layer.shadowColor = [UIColor blackColor].CGColor;
        _containerView.layer.shadowOffset = CGSizeMake(0, 8);
        _containerView.layer.shadowOpacity = 0.12;
        _containerView.layer.shadowRadius = 30.0;
        _containerView.layer.masksToBounds = NO;
    }
    return _containerView;
}

//- (UILabel *)titleLabel
//{
//    if (!_titleLabel) {
//        _titleLabel = [[UILabel alloc] init];
//        _titleLabel.text = @"阅读模式设置";
//        _titleLabel.font = [UIFont systemFontOfSize:16.0 weight:UIFontWeightSemibold];
//        _titleLabel.textColor = [UIColor colorWithHexString:@"#333333"];
//        _titleLabel.textAlignment = NSTextAlignmentCenter;
//
//        // 添加底部分割线
//        UIView *separatorLine = [[UIView alloc] init];
//        separatorLine.backgroundColor = [UIColor colorWithHexString:@"#f0f0f0"];
//        [_titleLabel addSubview:separatorLine];
//
//        [separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.left.right.bottom.equalTo(_titleLabel);
//            make.height.mas_equalTo(1);
//        }];
//    }
//    return _titleLabel;
//}

- (UIView *)fontSizeSection
{
    if (!_fontSizeSection) {
        _fontSizeSection = [[UIView alloc] init];
    }
    return _fontSizeSection;
}

- (UIView *)fontSizeHeaderView
{
    if (!_fontSizeHeaderView) {
        _fontSizeHeaderView = [[UIView alloc] init];
    }
    return _fontSizeHeaderView;
}

- (UILabel *)fontSizeLabel
{
    if (!_fontSizeLabel) {
        _fontSizeLabel = [[UILabel alloc] init];
        _fontSizeLabel.text = @"字体大小";
        _fontSizeLabel.font = [UIFont systemFontOfSize:14.0 weight:UIFontWeightSemibold];
        _fontSizeLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    return _fontSizeLabel;
}

- (UIButton *)exitReadingButton
{
    if (!_exitReadingButton) {
        _exitReadingButton = [UIButton buttonWithType:UIButtonTypeCustom];

        // 按钮样式 - 对应HTML中的exit-reading-btn样式
        _exitReadingButton.layer.cornerRadius = 20.0;
        _exitReadingButton.layer.borderWidth = 1.0;
        _exitReadingButton.layer.borderColor = [UIColor colorWithHexString:@"#e8e8e8"].CGColor;
        _exitReadingButton.backgroundColor = [UIColor colorWithHexString:@"#ffffff"];

        // 内容布局
        _exitReadingButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentCenter;
        _exitReadingButton.contentEdgeInsets = UIEdgeInsetsMake(6, 12, 6, 12);
        _exitReadingButton.titleEdgeInsets = UIEdgeInsetsMake(0, 6, 0, 0);

        // 图标设置
        [_exitReadingButton setImage:[UIImage systemImageNamed:@"xmark"] forState:UIControlStateNormal];
        _exitReadingButton.imageView.contentMode = UIViewContentModeScaleAspectFit;

        // 文字设置
        [_exitReadingButton setTitle:@"退出阅读模式" forState:UIControlStateNormal];
        _exitReadingButton.titleLabel.font = [UIFont systemFontOfSize:11.0 weight:UIFontWeightMedium];
        [_exitReadingButton setTitleColor:[UIColor colorWithHexString:@"#666666"] forState:UIControlStateNormal];

        // 图标颜色
        _exitReadingButton.tintColor = [UIColor colorWithHexString:@"#666666"];

        // 设置图标大小
        CGFloat iconSize = 10.0;
        [_exitReadingButton.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(iconSize);
        }];
    }
    return _exitReadingButton;
}

- (UIView *)fontSizeControlView
{
    if (!_fontSizeControlView) {
        _fontSizeControlView = [[UIView alloc] init];
        _fontSizeControlView.backgroundColor = [UIColor colorWithHexString:@"#f8f9fa"];
        _fontSizeControlView.layer.cornerRadius = 12.0;
    }
    return _fontSizeControlView;
}

- (UIButton *)decreaseFontButton
{
    if (!_decreaseFontButton) {
        _decreaseFontButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_decreaseFontButton setTitle:@"−" forState:UIControlStateNormal];
        _decreaseFontButton.titleLabel.font = [UIFont systemFontOfSize:18.0 weight:UIFontWeightSemibold];
        [_decreaseFontButton setTitleColor:[UIColor colorWithHexString:@"#4285f4"] forState:UIControlStateNormal];
        _decreaseFontButton.backgroundColor = [UIColor whiteColor];
        _decreaseFontButton.layer.cornerRadius = 18.0;
        _decreaseFontButton.layer.shadowColor = [UIColor blackColor].CGColor;
        _decreaseFontButton.layer.shadowOffset = CGSizeMake(0, 2);
        _decreaseFontButton.layer.shadowOpacity = 0.1;
        _decreaseFontButton.layer.shadowRadius = 4.0;
    }
    return _decreaseFontButton;
}

- (UILabel *)fontSizeDisplayLabel
{
    if (!_fontSizeDisplayLabel) {
        _fontSizeDisplayLabel = [[UILabel alloc] init];
        _fontSizeDisplayLabel.text = @"14px";
        _fontSizeDisplayLabel.font = [UIFont systemFontOfSize:16.0 weight:UIFontWeightSemibold];
        _fontSizeDisplayLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        _fontSizeDisplayLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _fontSizeDisplayLabel;
}

- (UIButton *)increaseFontButton
{
    if (!_increaseFontButton) {
        _increaseFontButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_increaseFontButton setTitle:@"+" forState:UIControlStateNormal];
        _increaseFontButton.titleLabel.font = [UIFont systemFontOfSize:18.0 weight:UIFontWeightSemibold];
        [_increaseFontButton setTitleColor:[UIColor colorWithHexString:@"#4285f4"] forState:UIControlStateNormal];
        _increaseFontButton.backgroundColor = [UIColor whiteColor];
        _increaseFontButton.layer.cornerRadius = 18.0;
        _increaseFontButton.layer.shadowColor = [UIColor blackColor].CGColor;
        _increaseFontButton.layer.shadowOffset = CGSizeMake(0, 2);
        _increaseFontButton.layer.shadowOpacity = 0.1;
        _increaseFontButton.layer.shadowRadius = 4.0;
    }
    return _increaseFontButton;
}

- (UIView *)backgroundSection
{
    if (!_backgroundSection) {
        _backgroundSection = [[UIView alloc] init];
    }
    return _backgroundSection;
}

- (UILabel *)backgroundLabel
{
    if (!_backgroundLabel) {
        _backgroundLabel = [[UILabel alloc] init];
        _backgroundLabel.text = @"背景颜色";
        _backgroundLabel.font = [UIFont systemFontOfSize:14.0 weight:UIFontWeightSemibold];
        _backgroundLabel.textColor = [UIColor colorWithHexString:@"#333333"];
    }
    return _backgroundLabel;
}

- (UIView *)backgroundOptionsView
{
    if (!_backgroundOptionsView) {
        _backgroundOptionsView = [[UIView alloc] init];
    }
    return _backgroundOptionsView;
}

@end
