//
//  ReadingModeThemeEnumExample.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingModeBackgroundTheme.h"

/*
 * 阅读模式主题枚举使用示例
 * 
 * 这个文件展示了如何使用ReadingModeThemeType枚举和相关的转换方法
 */

@implementation ReadingModeThemeEnumExample

#pragma mark - 枚举使用示例

+ (void)demonstrateEnumUsage
{
    NSLog(@"=== 阅读模式主题枚举使用示例 ===");
    
    // 1. 直接使用枚举创建主题
    ReadingModeBackgroundTheme *whiteTheme = [ReadingModeBackgroundTheme themeForType:ReadingModeThemeTypeWhite];
    NSLog(@"白色主题: %@", whiteTheme.displayName);
    
    ReadingModeBackgroundTheme *darkTheme = [ReadingModeBackgroundTheme themeForType:ReadingModeThemeTypeDark];
    NSLog(@"深色主题: %@", darkTheme.displayName);
    
    // 2. 枚举和字符串的转换
    NSString *whiteString = [ReadingModeBackgroundTheme stringForThemeType:ReadingModeThemeTypeWhite];
    NSLog(@"白色主题字符串: %@", whiteString);
    
    ReadingModeThemeType greenType = [ReadingModeBackgroundTheme themeTypeForString:@"green"];
    NSLog(@"绿色主题枚举值: %ld", (long)greenType);
    
    // 3. 遍历所有主题
    NSLog(@"\n=== 所有可用主题 ===");
    for (NSInteger i = 0; i < ReadingModeThemeTypeCount; i++) {
        ReadingModeThemeType themeType = (ReadingModeThemeType)i;
        ReadingModeBackgroundTheme *theme = [ReadingModeBackgroundTheme themeForType:themeType];
        NSString *themeString = [ReadingModeBackgroundTheme stringForThemeType:themeType];
        
        NSLog(@"主题 %ld: %@ (%@) - 背景: %@, 文字: %@, 暗色: %@",
              (long)themeType,
              theme.displayName,
              themeString,
              theme.backgroundColorHex,
              theme.textColorHex,
              theme.isDarkTheme ? @"是" : @"否");
    }
    
    // 4. 主题比较
    NSLog(@"\n=== 主题比较示例 ===");
    ReadingModeBackgroundTheme *theme1 = [ReadingModeBackgroundTheme themeForType:ReadingModeThemeTypeBlue];
    ReadingModeBackgroundTheme *theme2 = [ReadingModeBackgroundTheme themeForType:ReadingModeThemeTypeBlue];
    ReadingModeBackgroundTheme *theme3 = [ReadingModeBackgroundTheme themeForType:ReadingModeThemeTypePink];
    
    NSLog(@"蓝色主题1 == 蓝色主题2: %@", [theme1 isEqualToTheme:theme2] ? @"是" : @"否");
    NSLog(@"蓝色主题1 == 粉色主题: %@", [theme1 isEqualToTheme:theme3] ? @"是" : @"否");
    
    // 5. Switch语句使用示例
    [self handleThemeChange:ReadingModeThemeTypeDark];
    [self handleThemeChange:ReadingModeThemeTypeGreen];
    [self handleThemeChange:ReadingModeThemeTypeYellow];
}

+ (void)handleThemeChange:(ReadingModeThemeType)themeType
{
    NSLog(@"\n=== 处理主题变更: %ld ===", (long)themeType);
    
    switch (themeType) {
        case ReadingModeThemeTypeWhite:
            NSLog(@"应用白色主题 - 经典模式");
            break;
            
        case ReadingModeThemeTypeBeige:
            NSLog(@"应用米色主题 - 温暖模式");
            break;
            
        case ReadingModeThemeTypeDark:
            NSLog(@"应用深色主题 - 夜间模式");
            // 可以在这里添加特殊的夜间模式处理逻辑
            break;
            
        case ReadingModeThemeTypeGreen:
            NSLog(@"应用护眼绿主题 - 护眼模式");
            // 可以在这里添加护眼模式的特殊处理
            break;
            
        case ReadingModeThemeTypeBlue:
            NSLog(@"应用护眼蓝主题 - 舒缓模式");
            break;
            
        case ReadingModeThemeTypePink:
            NSLog(@"应用粉色主题 - 温柔模式");
            break;
            
        case ReadingModeThemeTypeYellow:
            NSLog(@"应用暖黄主题 - 纸张模式");
            break;
            
        case ReadingModeThemeTypeGray:
            NSLog(@"应用灰色主题 - 中性模式");
            break;
            
        case ReadingModeThemeTypePurple:
            NSLog(@"应用紫色主题 - 优雅模式");
            break;
            
        default:
            NSLog(@"未知主题类型，使用默认主题");
            break;
    }
}

#pragma mark - 实际应用示例

+ (void)saveThemeToUserDefaults:(ReadingModeThemeType)themeType
{
    // 保存枚举值到UserDefaults
    [[NSUserDefaults standardUserDefaults] setInteger:themeType forKey:@"selectedThemeType"];
    
    // 同时保存字符串格式（用于兼容性）
    NSString *themeString = [ReadingModeBackgroundTheme stringForThemeType:themeType];
    [[NSUserDefaults standardUserDefaults] setObject:themeString forKey:@"selectedThemeString"];
    
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    NSLog(@"已保存主题: %ld (%@)", (long)themeType, themeString);
}

+ (ReadingModeThemeType)loadThemeFromUserDefaults
{
    // 优先从枚举值加载
    NSInteger savedType = [[NSUserDefaults standardUserDefaults] integerForKey:@"selectedThemeType"];
    if (savedType >= 0 && savedType < ReadingModeThemeTypeCount) {
        NSLog(@"从枚举值加载主题: %ld", (long)savedType);
        return (ReadingModeThemeType)savedType;
    }
    
    // 兼容性处理：从字符串加载
    NSString *savedString = [[NSUserDefaults standardUserDefaults] stringForKey:@"selectedThemeString"];
    if (savedString) {
        ReadingModeThemeType themeType = [ReadingModeBackgroundTheme themeTypeForString:savedString];
        NSLog(@"从字符串加载主题: %@ -> %ld", savedString, (long)themeType);
        return themeType;
    }
    
    // 默认返回白色主题
    NSLog(@"使用默认主题: 白色");
    return ReadingModeThemeTypeWhite;
}

#pragma mark - 网络传输示例

+ (NSDictionary *)themeToJSON:(ReadingModeBackgroundTheme *)theme
{
    return @{
        @"type": @(theme.themeType),
        @"key": [ReadingModeBackgroundTheme stringForThemeType:theme.themeType],
        @"name": theme.displayName,
        @"backgroundColor": theme.backgroundColorHex,
        @"textColor": theme.textColorHex,
        @"isDark": @(theme.isDarkTheme)
    };
}

+ (ReadingModeBackgroundTheme *)themeFromJSON:(NSDictionary *)json
{
    // 优先使用type字段
    NSNumber *typeNumber = json[@"type"];
    if (typeNumber) {
        ReadingModeThemeType themeType = (ReadingModeThemeType)[typeNumber integerValue];
        if (themeType >= 0 && themeType < ReadingModeThemeTypeCount) {
            return [ReadingModeBackgroundTheme themeForType:themeType];
        }
    }
    
    // 兼容性处理：使用key字段
    NSString *key = json[@"key"];
    if (key) {
        return [ReadingModeBackgroundTheme themeForKey:key];
    }
    
    // 默认返回白色主题
    return [ReadingModeBackgroundTheme defaultTheme];
}

@end

/*
 * 枚举优化的优势：
 * 
 * 1. 类型安全：编译时检查，避免字符串拼写错误
 * 2. 性能优化：整数比较比字符串比较更快
 * 3. 代码提示：IDE可以提供完整的枚举值提示
 * 4. 重构友好：重命名枚举值时IDE可以自动更新所有引用
 * 5. Switch语句：编译器可以检查是否处理了所有枚举值
 * 6. 内存效率：枚举值占用更少内存
 * 7. 向后兼容：同时支持字符串和枚举，便于迁移
 * 8. 扩展性：添加新主题只需在枚举中添加新值
 * 9. 调试友好：枚举值在调试器中显示更清晰
 * 10. 文档化：枚举本身就是很好的文档
 */
