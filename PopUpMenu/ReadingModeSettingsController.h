//
//  ReadingModeSettingsController.h
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "BaseViewController.h"

@class ReadingModeBackgroundTheme;

@protocol ReadingModeSettingsDelegate <NSObject>

- (void)readingModeSettingsDidChangeFontSize:(NSInteger)fontSize;
- (void)readingModeSettingsDidChangeBackgroundTheme:(ReadingModeBackgroundTheme *)theme;

@end

@interface ReadingModeSettingsController : BaseViewController<UIPopoverPresentationControllerDelegate>

@property (nonatomic, weak) id<ReadingModeSettingsDelegate> delegate;

+ (void)showAt:(UIViewController *)controller sourceRect:(CGRect)sourceRect delegate:(id<ReadingModeSettingsDelegate>)delegate;

@end
