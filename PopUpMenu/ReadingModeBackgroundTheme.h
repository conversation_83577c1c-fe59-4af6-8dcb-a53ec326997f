//
//  ReadingModeBackgroundTheme.h
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 阅读模式背景主题类型枚举
 */
typedef NS_ENUM(NSInteger, ReadingModeThemeType) {
    ReadingModeThemeTypeWhite = 0,      // 白色主题
    ReadingModeThemeTypeBeige = 1,          // 米色主题
    ReadingModeThemeTypeDark = 2,           // 深色主题
    ReadingModeThemeTypeGreen = 3,          // 护眼绿主题
    ReadingModeThemeTypeBlue = 4,           // 护眼蓝主题
    ReadingModeThemeTypePink = 5,           // 粉色主题
    ReadingModeThemeTypeYellow = 6,         // 暖黄主题
    ReadingModeThemeTypeGray = 7,           // 灰色主题
    ReadingModeThemeTypePurple = 8,         // 紫色主题
    ReadingModeThemeTypeCount           // 主题总数（用于遍历）
};

/**
 * 阅读模式背景主题模型
 * 包含主题的所有必要信息：标识符、显示名称、背景颜色、文字颜色等
 */
@interface ReadingModeBackgroundTheme : NSObject

/// 主题类型枚举
@property (nonatomic, assign, readonly) ReadingModeThemeType themeType;

/// 主题显示名称
@property (nonatomic, strong, readonly) NSString *displayName;

/// 背景颜色（十六进制字符串）
@property (nonatomic, strong, readonly) NSString *backgroundColorHex;

/// 背景颜色（UIColor对象）
@property (nonatomic, strong, readonly) UIColor *backgroundColor;

/// 文字颜色（十六进制字符串）
@property (nonatomic, strong, readonly) NSString *textColorHex;

/// 文字颜色（UIColor对象）
@property (nonatomic, strong, readonly) UIColor *textColor;

/// 主题描述（可选）
@property (nonatomic, strong, readonly, nullable) NSString *themeDescription;

/// 是否为暗色主题
@property (nonatomic, assign, readonly) BOOL isDarkTheme;

/**
 * 初始化方法
 * @param themeType 主题类型枚举
 * @param displayName 显示名称
 * @param backgroundColorHex 背景颜色（十六进制）
 * @param textColorHex 文字颜色（十六进制）
 * @param themeDescription 主题描述
 * @param isDarkTheme 是否为暗色主题
 */
- (instancetype)initWithThemeType:(ReadingModeThemeType)themeType
                      displayName:(NSString *)displayName
                 backgroundColorHex:(NSString *)backgroundColorHex
                     textColorHex:(NSString *)textColorHex
                 themeDescription:(nullable NSString *)themeDescription
                      isDarkTheme:(BOOL)isDarkTheme;

/**
 * 便利初始化方法（自动判断是否为暗色主题）
 * @param themeType 主题类型枚举
 * @param displayName 显示名称
 * @param backgroundColorHex 背景颜色（十六进制）
 * @param textColorHex 文字颜色（十六进制）
 */
- (instancetype)initWithThemeType:(ReadingModeThemeType)themeType
                      displayName:(NSString *)displayName
                 backgroundColorHex:(NSString *)backgroundColorHex
                     textColorHex:(NSString *)textColorHex;

/**
 * 获取所有默认的背景主题
 * @return 包含9种默认主题的数组
 */
+ (NSArray<ReadingModeBackgroundTheme *> *)defaultBackgroundThemes;

/**
 * 根据主题类型获取对应的主题
 * @param themeType 主题类型枚举
 * @return 对应的主题对象
 */
+ (ReadingModeBackgroundTheme *)themeForType:(ReadingModeThemeType)themeType;

/**
 * 获取默认主题（白色主题）
 * @return 默认的白色主题
 */
+ (ReadingModeBackgroundTheme *)defaultTheme;

/**
 * 判断两个主题是否相等
 * @param theme 要比较的主题
 * @return 是否相等
 */
- (BOOL)isEqualToTheme:(ReadingModeBackgroundTheme *)theme;

@end

NS_ASSUME_NONNULL_END
