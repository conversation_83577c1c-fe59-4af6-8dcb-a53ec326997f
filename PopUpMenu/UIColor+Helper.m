//
//  UIColor+Helper.m
//  QRCode
//
//  Created by qingbin on 2021/11/7.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import "UIColor+Helper.h"

@implementation UIColor (Helper)

+ (UIColor *)colorWithHexString:(NSString *)hexString
{
    // 8位-前2位是alpha通道
    // 6位-纯颜色值,alpha=1
    
    NSString *colorString = hexString;
    if ([colorString hasPrefix:@"#"]) {
        colorString = [colorString substringFromIndex:1];
    } else if ([colorString.uppercaseString hasPrefix:@"0X"]) {
        colorString = [colorString substringFromIndex:2];
    }
    colorString = [colorString uppercaseString];
    
    CGFloat alpha, red, blue, green;
    switch ([colorString length]) {
        case 8: // #AARRGGBB
            alpha = strtoul([[colorString substringWithRange:NSMakeRange(0, 2)] UTF8String], NULL, 16) / 255.0f;
            red = strtoul([[colorString substringWithRange:NSMakeRange(2, 2)] UTF8String], NULL, 16) / 255.0f;
            green = strtoul([[colorString substringWithRange:NSMakeRange(4, 2)] UTF8String], NULL, 16) / 255.0f;
            blue = strtoul([[colorString substringWithRange:NSMakeRange(6, 2)] UTF8String], NULL, 16) / 255.0f;
            break;
        case 6: // #RRGGBB
            alpha = 1.0f;
            red = strtoul([[colorString substringWithRange:NSMakeRange(0, 2)] UTF8String], NULL, 16) / 255.0f;
            green = strtoul([[colorString substringWithRange:NSMakeRange(2, 2)] UTF8String], NULL, 16) / 255.0f;
            blue = strtoul([[colorString substringWithRange:NSMakeRange(4, 2)] UTF8String], NULL, 16) / 255.0f;
            break;
        default:
            return [UIColor clearColor];
    }
    
    return [UIColor colorWithRed:red green:green blue:blue alpha:alpha];
}

+ (UIImage *)imageWithColor:(UIColor *)color size:(CGSize)size
{
    CGRect rect = CGRectMake(0, 0, size.width, size.height);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return image;
}

// 2.6.4 根据给定url，返回一个指定的颜色值
+ (UIColor *)colorWithURL:(NSString *)url
{
    /*
     现在我需要设计一个更通用的方案，让不同网站有不同颜色，但又不是针对特定网站的硬编码方式。有几种可能的方案：

     1. 基于域名或URL的哈希值生成颜色 - 这样同一网站会有一致的颜色
     2. 使用预定义的颜色数组，基于某种规则（比如URL的某些特征）选择颜色
     3. 根据网站首字母或网站名称的某些特征生成颜色

     从用户体验来看，最好是：
     - 同一网站每次显示相同的颜色（一致性）
     - 颜色要美观，有足够的对比度
     - 相似的网站不要有太相近的颜色（区分性）
     - 颜色应该足够明亮和吸引人，但不要太刺眼

     我认为最好的方案是基于网站域名的哈希值来生成一组美观的预定义颜色中的一个。这样可以保证同一网站总是得到相同的颜色，同时又避免了硬编码针对特定网站。
     */
    
    // 根据URL获取域名
    NSString *domain = @"";
    NSURL *URL = [NSURL URLWithString:url];
    
    if (URL && URL.host) {
        domain = URL.host;
        // 如果域名以www.开头，去掉www.
        if ([domain hasPrefix:@"www."]) {
            domain = [domain substringFromIndex:4];
        }
    }
    
    // 预定义一组美观的颜色
    NSArray *colorSet = @[
        @"#4285F4", // 蓝色
        @"#EA4335", // 红色
        @"#FBBC05", // 黄色
        @"#34A853", // 绿色
        @"#5851DB", // 紫色
        @"#E1306C", // 粉红
        @"#2196F3", // 天蓝
        @"#FF9800", // 橙色
        @"#9C27B0", // 深紫
        @"#00BCD4", // 青色
        @"#3F51B5", // 靛蓝
        @"#009688", // 茶绿
        @"#FF5722", // 深橙
        @"#607D8B", // 蓝灰
        @"#795548", // 棕色
        @"#673AB7"  // 暗紫
    ];
    
    // 使用域名计算哈希值，选择颜色
    NSUInteger colorIndex = 0;
    if (domain.length > 0) {
        NSUInteger hash = 0;
        for (NSUInteger i = 0; i < domain.length; i++) {
            hash = hash * 31 + [domain characterAtIndex:i];
        }
        colorIndex = hash % colorSet.count;
    }
    
    // 设置背景色
    UIColor *bgColor = [UIColor colorWithHexString:colorSet[colorIndex]];
    return bgColor;
}

// 2.6.4 根据给定url，返回一个指定的渐变颜色值
+ (NSArray<NSString *> *)gradientColorWithURL:(NSString *)url
{
    /*
     现在我需要设计一个更通用的方案，让不同网站有不同颜色，但又不是针对特定网站的硬编码方式。有几种可能的方案：

     1. 基于域名或URL的哈希值生成颜色 - 这样同一网站会有一致的颜色
     2. 使用预定义的颜色数组，基于某种规则（比如URL的某些特征）选择颜色
     3. 根据网站首字母或网站名称的某些特征生成颜色

     从用户体验来看，最好是：
     - 同一网站每次显示相同的颜色（一致性）
     - 颜色要美观，有足够的对比度
     - 相似的网站不要有太相近的颜色（区分性）
     - 颜色应该足够明亮和吸引人，但不要太刺眼

     我认为最好的方案是基于网站域名的哈希值来生成一组美观的预定义颜色中的一个。这样可以保证同一网站总是得到相同的颜色，同时又避免了硬编码针对特定网站。
     */
    
    // 根据URL获取域名
    NSString *domain = @"";
    NSURL *URL = [NSURL URLWithString:url];
    
    if (URL && URL.host) {
        domain = URL.host;
        // 如果域名以www.开头，去掉www.
        if ([domain hasPrefix:@"www."]) {
            domain = [domain substringFromIndex:4];
        }
    }
    
    // 预定义一组美观的渐变色配对
    NSArray *gradientColors = @[
        @[@"#4F8EFF", @"#1A73E8"], // 谷歌蓝渐变
        @[@"#FF5252", @"#D32F2F"], // 红色渐变
        @[@"#FFD54F", @"#FFC107"], // 黄色渐变
        @[@"#66BB6A", @"#43A047"], // 绿色渐变
        @[@"#7986CB", @"#3F51B5"], // 靛蓝渐变
        @[@"#5C6BC0", @"#3949AB"], // 蓝紫渐变
        @[@"#EC407A", @"#D81B60"], // 桃红渐变
        @[@"#29B6F6", @"#0288D1"], // 浅蓝渐变
        @[@"#FFA726", @"#F57C00"], // 橙色渐变
        @[@"#AB47BC", @"#8E24AA"], // 紫色渐变
        @[@"#26C6DA", @"#00ACC1"], // 青色渐变
        @[@"#26A69A", @"#00897B"], // 茶绿渐变
        @[@"#FF7043", @"#E64A19"], // 深橙渐变
        @[@"#78909C", @"#546E7A"], // 蓝灰渐变
        @[@"#8D6E63", @"#6D4C41"], // 棕色渐变
        @[@"#9575CD", @"#7E57C2"]  // 紫色渐变
    ];
    
    // 使用域名计算哈希值，选择颜色
    NSUInteger colorIndex = 0;
    if (domain.length > 0) {
        NSUInteger hash = 0;
        for (NSUInteger i = 0; i < domain.length; i++) {
            hash = hash * 31 + [domain characterAtIndex:i];
        }
        colorIndex = hash % gradientColors.count;
    }
    
    return gradientColors[colorIndex];
}

@end
