//
//  ReadingModeBackgroundTheme.m
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "ReadingModeBackgroundTheme.h"
#import "UIColor+Helper.h"

@interface ReadingModeBackgroundTheme ()

@property (nonatomic, assign, readwrite) ReadingModeThemeType themeType;
@property (nonatomic, strong, readwrite) NSString *displayName;
@property (nonatomic, strong, readwrite) NSString *backgroundColorHex;
@property (nonatomic, strong, readwrite) UIColor *backgroundColor;
@property (nonatomic, strong, readwrite) NSString *textColorHex;
@property (nonatomic, strong, readwrite) UIColor *textColor;
@property (nonatomic, strong, readwrite, nullable) NSString *themeDescription;
@property (nonatomic, assign, readwrite) BOOL isDarkTheme;

@end

@implementation ReadingModeBackgroundTheme

#pragma mark - 初始化方法

- (instancetype)initWithThemeType:(ReadingModeThemeType)themeType
                      displayName:(NSString *)displayName
                 backgroundColorHex:(NSString *)backgroundColorHex
                     textColorHex:(NSString *)textColorHex
                 themeDescription:(nullable NSString *)themeDescription
                      isDarkTheme:(BOOL)isDarkTheme
{
    self = [super init];
    if (self) {
        _themeType = themeType;
        _displayName = [displayName copy];
        _backgroundColorHex = [backgroundColorHex copy];
        _backgroundColor = [UIColor colorWithHexString:backgroundColorHex];
        _textColorHex = [textColorHex copy];
        _textColor = [UIColor colorWithHexString:textColorHex];
        _themeDescription = [themeDescription copy];
        _isDarkTheme = isDarkTheme;
    }
    return self;
}

- (instancetype)initWithThemeType:(ReadingModeThemeType)themeType
                      displayName:(NSString *)displayName
                 backgroundColorHex:(NSString *)backgroundColorHex
                     textColorHex:(NSString *)textColorHex
{
    // 自动判断是否为暗色主题（基于背景颜色的亮度）
    UIColor *bgColor = [UIColor colorWithHexString:backgroundColorHex];
    BOOL isDark = [self isDarkColor:bgColor];

    return [self initWithThemeType:themeType
                       displayName:displayName
                  backgroundColorHex:backgroundColorHex
                      textColorHex:textColorHex
                  themeDescription:nil
                       isDarkTheme:isDark];
}

#pragma mark - 类方法

+ (NSArray<ReadingModeBackgroundTheme *> *)defaultBackgroundThemes
{
    static NSArray<ReadingModeBackgroundTheme *> *_defaultThemes = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _defaultThemes = @[
            // 1. 白色主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeWhite
                                                      displayName:@"白色"
                                                 backgroundColorHex:@"#ffffff"
                                                     textColorHex:@"#333333"
                                                 themeDescription:@"经典白色背景，适合日间阅读"
                                                      isDarkTheme:NO],

            // 2. 米色主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeBeige
                                                      displayName:@"米色"
                                                 backgroundColorHex:@"#f5f5dc"
                                                     textColorHex:@"#333333"
                                                 themeDescription:@"温暖的米色背景，减少眩光"
                                                      isDarkTheme:NO],

            // 3. 深色主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeDark
                                                      displayName:@"深色"
                                                 backgroundColorHex:@"#2d3748"
                                                     textColorHex:@"#e2e8f0"
                                                 themeDescription:@"深色背景，适合夜间阅读"
                                                      isDarkTheme:YES],

            // 4. 护眼绿主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeGreen
                                                      displayName:@"护眼绿"
                                                 backgroundColorHex:@"#c6f6d5"
                                                     textColorHex:@"#2d3748"
                                                 themeDescription:@"淡绿色背景，保护视力"
                                                      isDarkTheme:NO],

            // 5. 护眼蓝主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeBlue
                                                      displayName:@"护眼蓝"
                                                 backgroundColorHex:@"#e6f3ff"
                                                     textColorHex:@"#1e40af"
                                                 themeDescription:@"淡蓝色背景，舒缓眼部疲劳"
                                                      isDarkTheme:NO],

            // 6. 粉色主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypePink
                                                      displayName:@"粉色"
                                                 backgroundColorHex:@"#fce7f3"
                                                     textColorHex:@"#be185d"
                                                 themeDescription:@"温柔的粉色背景"
                                                      isDarkTheme:NO],

            // 7. 暖黄主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeYellow
                                                      displayName:@"暖黄"
                                                 backgroundColorHex:@"#fef3c7"
                                                     textColorHex:@"#92400e"
                                                 themeDescription:@"温暖的黄色背景，模拟纸张质感"
                                                      isDarkTheme:NO],

            // 8. 灰色主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypeGray
                                                      displayName:@"灰色"
                                                 backgroundColorHex:@"#f3f4f6"
                                                     textColorHex:@"#374151"
                                                 themeDescription:@"中性灰色背景，平衡对比度"
                                                      isDarkTheme:NO],

            // 9. 紫色主题
            [[ReadingModeBackgroundTheme alloc] initWithThemeType:ReadingModeThemeTypePurple
                                                      displayName:@"紫色"
                                                 backgroundColorHex:@"#ede9fe"
                                                     textColorHex:@"#6b21a8"
                                                 themeDescription:@"优雅的紫色背景"
                                                      isDarkTheme:NO]
        ];
    });

    return _defaultThemes;
}

+ (ReadingModeBackgroundTheme *)themeForType:(ReadingModeThemeType)themeType
{
    NSArray<ReadingModeBackgroundTheme *> *themes = [self defaultBackgroundThemes];
    for (ReadingModeBackgroundTheme *theme in themes) {
        if (theme.themeType == themeType) {
            return theme;
        }
    }

    // 如果未找到，返回默认主题
    return [self defaultTheme];
}

+ (ReadingModeBackgroundTheme *)defaultTheme
{
    return [self themeForType:ReadingModeThemeTypeWhite]; // 白色主题作为默认主题
}

#pragma mark - 实例方法

- (BOOL)isEqualToTheme:(ReadingModeBackgroundTheme *)theme
{
    if (!theme) {
        return NO;
    }

    return self.themeType == theme.themeType;
}

#pragma mark - 私有方法

/**
 * 判断颜色是否为暗色
 * @param color 要判断的颜色
 * @return 是否为暗色
 */
- (BOOL)isDarkColor:(UIColor *)color
{
    CGFloat red, green, blue, alpha;
    if ([color getRed:&red green:&green blue:&blue alpha:&alpha]) {
        // 使用相对亮度公式判断
        CGFloat luminance = 0.299 * red + 0.587 * green + 0.114 * blue;
        return luminance < 0.5;
    }
    return NO;
}

#pragma mark - NSObject

- (NSString *)description
{
    return [NSString stringWithFormat:@"<%@: %p> themeType=%ld, displayName=%@, backgroundColor=%@, textColor=%@, isDarkTheme=%@",
            NSStringFromClass([self class]), self, (long)self.themeType, self.displayName,
            self.backgroundColorHex, self.textColorHex, self.isDarkTheme ? @"YES" : @"NO"];
}

- (BOOL)isEqual:(id)object
{
    if (self == object) {
        return YES;
    }

    if (![object isKindOfClass:[ReadingModeBackgroundTheme class]]) {
        return NO;
    }

    return [self isEqualToTheme:(ReadingModeBackgroundTheme *)object];
}

- (NSUInteger)hash
{
    return (NSUInteger)self.themeType;
}

@end
