//
//  ReadingModeSettings.h
//  PPBrowser
//
//  Created by qingbin on 2024/12/19.
//  Copyright © 2024 qingbin. All rights reserved.
//

#ifndef ReadingModeSettings_h
#define ReadingModeSettings_h

// 阅读模式设置相关的所有头文件
#import "ReadingModeBackgroundTheme.h"
#import "ReadingModeSettingsController.h"

/**
 * 阅读模式设置模块
 * 
 * 这个模块提供了完整的阅读模式设置功能，包括：
 * 1. 字体大小调整（12px-20px）
 * 2. 背景主题选择（9种预设主题）
 * 3. iOS原生Popover弹窗实现
 * 4. 设置的自动保存和加载
 * 5. 暗黑模式适配
 * 
 * 主要组件：
 * - ReadingModeBackgroundTheme: 背景主题模型类
 * - ReadingModeSettingsController: 设置弹窗控制器
 * - ReadingModeSettingsDelegate: 设置变更代理协议
 * 
 * 使用方法：
 * 1. 导入头文件：#import "ReadingModeSettings.h"
 * 2. 遵循代理协议：<ReadingModeSettingsDelegate>
 * 3. 显示弹窗：[ReadingModeSettingsController showAt:self sourceRect:rect delegate:self]
 * 4. 实现代理方法处理设置变更
 * 
 * 特性：
 * - 完全还原HTML版本的UI样式（圆角、阴影、字体等）
 * - 使用Model-View-Controller架构
 * - 支持iOS暗黑模式
 * - 自动保存用户设置
 * - 代理模式，便于集成
 */

#endif /* ReadingModeSettings_h */
