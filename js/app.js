// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 更新状态栏时间
    updateTime();
    setInterval(updateTime, 60000); // 每分钟更新一次
    
    // 底部标签栏切换
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            tabItems.forEach(tab => tab.classList.remove('active'));
            this.classList.add('active');
        });
    });
});

// 更新状态栏时间
function updateTime() {
    const timeElements = document.querySelectorAll('.time');
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const timeString = `${hours}:${minutes}`;
    
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
} 