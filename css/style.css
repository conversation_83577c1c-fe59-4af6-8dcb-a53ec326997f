/* 全局样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* iPhone 样式 */
.prototype-container {
    margin-bottom: 40px;
}

.iphone-container {
    width: 390px; /* iPhone 15 Pro 宽度 */
    height: 844px; /* iPhone 15 Pro 高度 */
    background-color: #000;
    border-radius: 55px;
    padding: 12px;
    position: relative;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    margin: 0 auto;
}

.iphone-screen {
    width: 100%;
    height: 100%;
    border-radius: 45px;
    overflow: hidden;
    background-color: #fff;
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.iphone-screen::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 公共组件样式 */
.ios-status-bar {
    height: 44px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
}

.status-left {
    display: flex;
    align-items: center;
}

.status-right {
    display: flex;
    align-items: center;
}

.time {
    font-weight: 600;
    font-size: 15px;
}

.battery-icon, .wifi-icon, .signal-icon {
    margin-left: 6px;
}

.tab-bar {
    height: 84px;
    background-color: rgba(255, 255, 255, 0.95);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 0;
    color: #8e8e93;
}

.tab-item.active {
    color: #007aff;
}

.tab-icon {
    font-size: 24px;
    margin-bottom: 4px;
}

.tab-label {
    font-size: 10px;
    font-weight: 500;
} 