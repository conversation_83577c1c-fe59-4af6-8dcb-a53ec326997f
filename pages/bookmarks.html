<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 书签</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            height: 812px;
            width: 375px;
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 40px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        }
        
        /* 隐藏滚动条但保留功能 */
        ::-webkit-scrollbar {
            width: 0;
            background: transparent;
        }
        
        .bookmark-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .bookmark-item:last-child {
            border-bottom: none;
        }
        
        .bookmark-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .folder-icon {
            background-color: #4F46E5;
        }
        
        .nav-button {
            height: 36px;
            width: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        
        .nav-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-white w-full overflow-hidden">
    <!-- 内容区域 -->
    <div class="content-area h-full overflow-y-auto relative pt-4 pb-6">
        <!-- 导航栏 -->
        <div class="nav-bar px-6 py-2 flex items-center justify-between">
            <div class="w-12"></div>
            <div class="text-lg font-bold text-black">书签</div>
            <div class="flex items-center space-x-2">
                <button class="nav-button text-gray-500">
                    <i class="fas fa-sort"></i>
                </button>
                <button class="nav-button text-gray-500">
                    <i class="fas fa-ellipsis-vertical"></i>
                </button>
            </div>
        </div>
        
        <!-- 书签搜索 -->
        <div class="px-4 py-2">
            <div class="bg-gray-100 rounded-lg px-3 py-2 flex items-center">
                <i class="fas fa-search text-gray-500 mr-2"></i>
                <input type="text" placeholder="搜索书签" class="bg-transparent w-full outline-none text-sm">
            </div>
        </div>
        
        <!-- 书签列表 -->
        <div class="px-4 py-2">
            <div class="bg-white rounded-lg">
                <!-- 常用网站文件夹 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon folder-icon mr-3">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">常用网站</div>
                        <div class="text-xs text-gray-500">12 项</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                
                <!-- 购物网站文件夹 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon folder-icon mr-3">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">购物网站</div>
                        <div class="text-xs text-gray-500">8 项</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                
                <!-- 学习资源文件夹 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon folder-icon mr-3">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">学习资源</div>
                        <div class="text-xs text-gray-500">15 项</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                
                <!-- 社交媒体文件夹 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon folder-icon mr-3">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">社交媒体</div>
                        <div class="text-xs text-gray-500">10 项</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
                
                <!-- 百度 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon mr-3" style="background-color: #3388FF;">
                        <span class="text-white font-bold">百</span>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">百度一下，你就知道</div>
                        <div class="text-xs text-gray-500">www.baidu.com</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                
                <!-- 知乎 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon mr-3" style="background-color: #0066FF;">
                        <span class="text-white font-bold">知</span>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">知乎 - 有问题，就会有答案</div>
                        <div class="text-xs text-gray-500">www.zhihu.com</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                
                <!-- 哔哩哔哩 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon mr-3" style="background-color: #FB7299;">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">哔哩哔哩 - 国内知名视频弹幕网站</div>
                        <div class="text-xs text-gray-500">www.bilibili.com</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                
                <!-- 微博 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon mr-3" style="background-color: #E6162D;">
                        <i class="fab fa-weibo"></i>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">微博 - 随时随地发现新鲜事</div>
                        <div class="text-xs text-gray-500">www.weibo.com</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                
                <!-- 淘宝 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon mr-3" style="background-color: #FF4400;">
                        <span class="text-white font-bold">淘</span>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">淘宝网 - 淘！我喜欢</div>
                        <div class="text-xs text-gray-500">www.taobao.com</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
                
                <!-- 京东 -->
                <div class="bookmark-item py-3 px-3 flex items-center">
                    <div class="bookmark-icon mr-3" style="background-color: #E1251B;">
                        <span class="text-white font-bold">京</span>
                    </div>
                    <div class="flex-grow">
                        <div class="text-sm font-medium">京东 - 多快好省</div>
                        <div class="text-xs text-gray-500">www.jd.com</div>
                    </div>
                    <div class="text-gray-400">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 