<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览Google (折叠设计)</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* 自定义样式 */
        .search-bar {
            border-radius: 24px;
            border: 1px solid #dfe1e5;
            box-shadow: 0 1px 6px rgba(32, 33, 36, 0.16);
            transition: all 0.3s ease;
            font-size: 12px;
            height: 34px;
        }
        
        .search-bar:focus-within {
            box-shadow: 0 1px 12px rgba(32, 33, 36, 0.28);
            border-color: transparent;
        }
        
        .google-results {
            font-family: Arial, sans-serif;
        }
        
        .search-result {
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .search-result:hover {
            background-color: #f8f9fa;
        }
        
        .circle-button {
            width: 34px;
            height: 34px;
            min-width: 34px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        
        .circle-button:hover {
            background-color: #f1f3f4;
        }
        
        .expanded-toolbar {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .search-bar-active .expanded-toolbar {
            max-height: 50px;
        }
        
        .action-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 4px 12px;
            font-size: 11px;
        }
        
        .action-button i {
            font-size: 18px;
            margin-bottom: 2px;
        }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 浏览器顶部搜索栏 - 折叠设计 -->
    <div class="browser-top-bar bg-white px-3 pt-14 pb-2 sticky top-0 z-10 border-b border-gray-200 search-bar-active">
        <!-- 主搜索栏 -->
        <div class="flex items-center gap-2 relative mb-2">
            <div class="search-bar flex items-center flex-1 px-3 py-1 relative" id="searchToggle">
                <img src="https://www.google.com/favicon.ico" class="w-5 h-5 mr-2" alt="Google">
                <div class="flex-1 truncate text-sm font-medium">
                    modern web design
                </div>
            </div>
            
            <!-- 右侧菜单按钮 -->
            <div class="circle-button relative" title="菜单">
                <i class="fas fa-ellipsis-v text-gray-600"></i>
            </div>
        </div>
        
        <!-- 展开后的工具栏 (默认隐藏，点击搜索栏后显示) -->
        <div class="expanded-toolbar bg-gray-50 -mx-3 px-2 py-1">
            <div class="flex justify-between">
                <div class="action-button text-blue-500">
                    <i class="fas fa-code"></i>
                    <span>脚本</span>
                </div>
                <div class="action-button text-gray-600">
                    <i class="fas fa-redo-alt"></i>
                    <span>刷新</span>
                </div>
                <div class="action-button text-gray-600">
                    <i class="fas fa-bookmark"></i>
                    <span>书签</span>
                </div>
                <div class="action-button text-gray-600">
                    <i class="fas fa-download"></i>
                    <span>下载</span>
                </div>
                <div class="action-button text-gray-600">
                    <i class="fas fa-desktop"></i>
                    <span>桌面版</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Google 搜索结果内容区域 -->
    <div class="content-area google-results pt-2 pb-20 h-full overflow-y-auto">
        <!-- Google Logo -->
        <div class="flex justify-center mt-2 mb-6">
            <img src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png" alt="Google" class="h-7">
        </div>
        
        <!-- 搜索信息栏 -->
        <div class="text-sm text-gray-600 px-4 pb-2 border-b border-gray-200">
            约 1,760,000,000 条结果 (0.48 秒)
        </div>
        
        <!-- 搜索结果 -->
        <div class="search-results px-4 py-3">
            <!-- 结果 1 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.example.com › modern-web-design</div>
                <div class="text-blue-700 text-lg font-medium mb-1">2023现代网页设计趋势与最佳实践 - Example</div>
                <div class="text-sm text-gray-800">现代网页设计强调简洁性、响应式布局和用户体验。了解最新的设计趋势，包括深色模式、微交互和沉浸式3D效果...</div>
            </div>
            
            <!-- 结果 2 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.webdesigntrends.com</div>
                <div class="text-blue-700 text-lg font-medium mb-1">网页设计的10大现代趋势 | Web Design Trends</div>
                <div class="text-sm text-gray-800">发现2023年主导网页设计的关键趋势。从新拟态UI到声音设计，了解当今最流行的设计方向...</div>
            </div>
            
            <!-- 结果 3 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.designacademy.edu/blog</div>
                <div class="text-blue-700 text-lg font-medium mb-1">如何掌握现代网页设计的基础 - Design Academy</div>
                <div class="text-sm text-gray-800">现代网页设计结合了美学和功能性。本指南介绍了核心原则，包括排版、色彩理论、空间利用和视觉层次结构...</div>
            </div>
            
            <!-- 结果 4 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.creativestudio.com/resources</div>
                <div class="text-blue-700 text-lg font-medium mb-1">30个令人惊艳的现代网页设计案例 - Creative Studio</div>
                <div class="text-sm text-gray-800">从全球顶尖品牌和创意工作室中精选的30个杰出网站设计。每个案例都包含详细分析和关键设计要素解释...</div>
            </div>
            
            <!-- 结果 5 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.techinsider.io/web-design</div>
                <div class="text-blue-700 text-lg font-medium mb-1">现代网页设计必备的5款设计工具 - Tech Insider</div>
                <div class="text-sm text-gray-800">探索专业设计师推荐的顶级网页设计工具。从Figma到Adobe XD，这些工具将帮助你创建优雅且用户友好的网站...</div>
            </div>
            
            <!-- 相关搜索 -->
            <div class="related-searches mt-8">
                <div class="text-lg text-gray-800 mb-3">相关搜索</div>
                <div class="grid grid-cols-2 gap-2">
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">现代网页设计课程</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">网页设计最新技术</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">UI/UX 设计趋势</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">前端开发框架比较</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <div class="tab-icon"><i class="fas fa-home"></i></div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-layer-group"></i></div>
            <div class="tab-label">标签页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-bookmark"></i></div>
            <div class="tab-label">书签</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-history"></i></div>
            <div class="tab-label">历史</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-cog"></i></div>
            <div class="tab-label">设置</div>
        </div>
    </div>
    
    <script>
        // 简单模拟点击搜索栏展开/折叠功能
        document.getElementById('searchToggle').addEventListener('click', function() {
            document.querySelector('.browser-top-bar').classList.toggle('search-bar-active');
        });
    </script>
    
    <script src="../js/app.js"></script>
</body>
</html> 