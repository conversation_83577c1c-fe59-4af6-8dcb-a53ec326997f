<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - iPad会员介绍</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            max-width: 1024px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            background-color: #f9fafb;
        }
        .top-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            background-color: #fff;
            max-width: 1024px;
            margin: 0 auto;
        }
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 16px 20px;
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        .nav-back {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 18px;
        }
        .nav-restore {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #007aff;
            font-size: 15px;
            font-weight: 500;
        }
        .nav-title {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }
        .content-area {
            position: absolute;
            top: 100px; /* 状态栏+导航栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 30px;
            display: flex;
        }
        .member-left {
            flex: 1;
            padding-right: 20px;
            position: relative;
        }
        .member-right {
            flex: 1;
            padding-left: 20px;
            display: flex;
            flex-direction: column;
        }
        .member-header {
            background: linear-gradient(135deg, #3751FF 0%, #3481FF 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 20px rgba(55, 81, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        .member-header::before {
            content: "";
            position: absolute;
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
            top: -200px;
            right: -200px;
            border-radius: 50%;
        }
        .member-header::after {
            content: "";
            position: absolute;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%);
            bottom: -150px;
            left: -150px;
            border-radius: 50%;
        }
        .member-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
        }
        .member-subtitle {
            opacity: 0.9;
            font-size: 16px;
            margin-bottom: 24px;
            position: relative;
        }
        .member-price {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 6px;
            position: relative;
        }
        .member-price-desc {
            opacity: 0.8;
            font-size: 16px;
            position: relative;
            margin-bottom: 24px;
        }
        .member-feature {
            background-color: white;
            border-radius: 14px;
            padding: 18px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .member-feature:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
        }
        .member-feature::after {
            content: "";
            position: absolute;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, rgba(var(--feature-color-rgb), 0.08) 0%, rgba(var(--feature-color-rgb), 0) 70%);
            bottom: -60px;
            right: -60px;
            border-radius: 50%;
        }
        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 18px;
            flex-shrink: 0;
            color: white;
            font-size: 22px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .feature-content {
            flex: 1;
        }
        .feature-title {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 6px;
            color: #1f2937;
        }
        .feature-desc {
            font-size: 14px;
            color: #4b5563;
            line-height: 1.5;
        }
        .action-buttons {
            margin-top: auto;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3751FF 0%, #3481FF 100%);
            color: white;
            font-weight: 600;
            padding: 16px 24px;
            border-radius: 14px;
            text-align: center;
            box-shadow: 0 4px 10px rgba(55, 81, 255, 0.2);
            font-size: 16px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(55, 81, 255, 0.3);
        }
        .crown-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            position: relative;
            z-index: 1;
        }
        .crown-icon::after {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            z-index: -1;
            animation: pulse 2s infinite;
        }
        .member-benefits {
            padding: 24px;
            background-color: white;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        .member-benefits::before {
            content: "";
            position: absolute;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(55, 81, 255, 0.05) 0%, rgba(55, 81, 255, 0) 70%);
            top: -75px;
            right: -75px;
            border-radius: 50%;
        }
        .benefits-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1f2937;
            text-align: center;
        }
        .benefits-desc {
            font-size: 15px;
            color: #4b5563;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 24px;
        }
        .floating-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background: linear-gradient(135deg, #FF6B6B 0%, #FF9F43 100%);
            color: white;
            border-radius: 20px;
            padding: 3px 10px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(255, 107, 107, 0.3);
            z-index: 2;
        }
        .rotating-icon {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 100px;
            height: 100px;
            opacity: 0.07;
            transform-origin: center;
            animation: rotate 20s linear infinite;
            z-index: 0;
        }
        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 24px;
        }
        .advantage-item {
            background-color: rgba(55, 81, 255, 0.05);
            border-radius: 10px;
            padding: 12px;
            display: flex;
            align-items: center;
        }
        .advantage-icon {
            width: 32px;
            height: 32px;
            background-color: rgba(55, 81, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3751FF;
            margin-right: 10px;
            flex-shrink: 0;
        }
        .advantage-text {
            font-size: 14px;
            font-weight: 500;
            color: #4b5563;
        }
        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
        .star {
            position: absolute;
            background-color: white;
            border-radius: 50%;
            opacity: 0.4;
            animation: twinkle 3s infinite;
        }
        @keyframes twinkle {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.7; }
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            70% {
                transform: scale(1.3);
                opacity: 0;
            }
            100% {
                transform: scale(1.3);
                opacity: 0;
            }
        }
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部容器 -->
    <div class="top-container">
        <!-- iOS 状态栏 -->
        <div class="ios-status-bar">
            <div class="status-left">
                <span class="time">14:42</span>
            </div>
            <div class="status-right">
                <span class="signal-icon"><i class="fas fa-signal"></i></span>
                <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
                <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-back">
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="nav-title">会员介绍</div>
            <div class="nav-restore">恢复购买</div>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 左侧 -->
        <div class="member-left">
            <!-- 星星粒子效果 -->
            <div class="stars"></div>
            
            <!-- 会员头部 -->
            <div class="member-header">
                <div class="crown-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="member-title">Pro高级会员</div>
                <div class="member-subtitle">解锁全部高级功能，享受极致浏览体验</div>
                <div class="member-price">¥38</div>
                <div class="member-price-desc">一次付费，永久使用</div>
                
                <!-- 购买会员按钮 -->
                <button class="btn-primary">购买会员</button>
            </div>
            
            <!-- 会员优势说明 -->
            <div class="member-benefits">
                <div class="benefits-title">为什么选择我们的Pro会员？</div>
                <div class="benefits-desc">
                    我们致力于提供最优质的浏览体验，Pro会员不仅可以享受无广告浏览，还能使用一系列专为效率与体验设计的高级功能。
                    一次付费，终身受益，所有功能随时更新，让您的浏览体验更加畅快。
                </div>
                
                <!-- 会员优势网格 -->
                <div class="advantages-grid">
                    <div class="advantage-item">
                        <div class="advantage-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <div class="advantage-text">一次付费终身使用</div>
                    </div>
                    <div class="advantage-item">
                        <div class="advantage-icon">
                            <i class="fas fa-sync"></i>
                        </div>
                        <div class="advantage-text">持续功能更新</div>
                    </div>
                    <div class="advantage-item">
                        <div class="advantage-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="advantage-text">隐私数据保护</div>
                    </div>
                    <div class="advantage-item">
                        <div class="advantage-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="advantage-text">优先客服支持</div>
                    </div>
                </div>
                
                <!-- 装饰图标 -->
                <div class="rotating-icon">
                    <i class="fas fa-crown" style="font-size: 100px;"></i>
                </div>
            </div>
        </div>
        
        <!-- 右侧 -->
        <div class="member-right">
            <!-- 会员功能列表 -->
            <div class="member-features">
                <!-- 功能1：无广告体验 -->
                <div class="member-feature" style="--feature-color-rgb: 255, 59, 48">
                    <div class="floating-badge">热门</div>
                    <div class="feature-icon" style="background-color: #FF3B30;">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">永久无广告</div>
                        <div class="feature-desc">专属会员体验，浏览网页再无广告打扰，让您的阅读体验更加纯净</div>
                    </div>
                </div>
                
                <!-- 功能2：手动标记 -->
                <div class="member-feature" style="--feature-color-rgb: 255, 149, 0">
                    <div class="feature-icon" style="background-color: #FF9500;">
                        <i class="fas fa-highlighter"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">手动标记</div>
                        <div class="feature-desc">轻松标记网页元素，一键去除不想看到的内容，自定义您的浏览体验</div>
                    </div>
                </div>
                
                <!-- 功能3：双引擎翻译 -->
                <div class="member-feature" style="--feature-color-rgb: 90, 200, 250">
                    <div class="feature-icon" style="background-color: #5AC8FA;">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">双引擎翻译</div>
                        <div class="feature-desc">网页一键翻译，支持多种翻译引擎和语言，让您畅游全球网站无障碍</div>
                    </div>
                </div>
                
                <!-- 功能4：油猴增强 -->
                <div class="member-feature" style="--feature-color-rgb: 88, 86, 214">
                    <div class="floating-badge">高级</div>
                    <div class="feature-icon" style="background-color: #5856D6;">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">油猴增强</div>
                        <div class="feature-desc">支持脚本更新与黑白名单等高级功能，让网页功能更丰富、使用更便捷</div>
                    </div>
                </div>
                
                <!-- 功能5：自动翻页 -->
                <div class="member-feature" style="--feature-color-rgb: 175, 82, 222">
                    <div class="feature-icon" style="background-color: #AF52DE;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">自动翻页</div>
                        <div class="feature-desc">网页自动翻页和智能拼页等高级特性，阅读长文章不再需要频繁点击</div>
                    </div>
                </div>
                
                <!-- 功能6：iCloud同步 -->
                <div class="member-feature" style="--feature-color-rgb: 100, 210, 255">
                    <div class="feature-icon" style="background-color: #64D2FF;">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">iCloud同步</div>
                        <div class="feature-desc">支持书签、脚本和标记等数据备份，换设备也不丢失您的个性化设置</div>
                    </div>
                </div>
                
                <!-- 功能7：多平台支持 -->
                <div class="member-feature" style="--feature-color-rgb: 52, 199, 89">
                    <div class="feature-icon" style="background-color: #34C759;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">多平台支持</div>
                        <div class="feature-desc">支持iPhone/iPad多设备同时使用，一次购买多处受益</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 创建星星背景
            createStars();
            
            // 添加功能卡片的悬停效果
            const features = document.querySelectorAll('.member-feature');
            features.forEach(feature => {
                feature.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                    this.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.1)';
                });
                feature.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });
        });
        
        // 创建星星效果
        function createStars() {
            const starsContainer = document.querySelector('.stars');
            const starsCount = 40;
            
            for (let i = 0; i < starsCount; i++) {
                const star = document.createElement('div');
                star.classList.add('star');
                
                // 随机位置
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                
                // 随机大小
                const size = Math.random() * 2.5 + 1;
                
                // 随机动画延迟
                const delay = Math.random() * 3;
                
                star.style.left = `${x}%`;
                star.style.top = `${y}%`;
                star.style.width = `${size}px`;
                star.style.height = `${size}px`;
                star.style.animationDelay = `${delay}s`;
                
                starsContainer.appendChild(star);
            }
        }
    </script>
</body>
</html>