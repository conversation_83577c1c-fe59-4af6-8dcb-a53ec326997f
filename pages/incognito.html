<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 隐私浏览</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background-color: #1c1c1e;
            color: #fff;
        }
        .ios-status-bar {
            background-color: #1c1c1e;
        }
        .search-bar {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .shortcut-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 24px;
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }
        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .shortcut-label {
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
        }
        .tab-bar {
            background-color: rgba(30, 30, 30, 0.95);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .privacy-info {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
        }
    </style>
</head>
<body class="w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area pt-14 pb-20 h-full overflow-y-auto relative">
        <!-- 导航栏 -->
        <div class="nav-bar px-4 py-2 flex items-center justify-between">
            <div class="text-lg font-bold text-purple-400">隐私浏览模式</div>
            <div class="flex items-center">
                <button class="w-8 h-8 flex items-center justify-center text-gray-400">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="w-8 h-8 flex items-center justify-center text-gray-400">
                    <i class="fas fa-user-circle"></i>
                </button>
            </div>
        </div>
        
        <!-- 搜索栏 -->
        <div class="px-4 py-3">
            <div class="search-bar flex items-center px-3 py-2">
                <i class="fas fa-search text-gray-400 mr-2"></i>
                <input type="text" placeholder="搜索或输入网址" class="bg-transparent w-full outline-none text-base text-white">
                <i class="fas fa-microphone text-gray-400 ml-2"></i>
            </div>
        </div>
        
        <!-- 隐私说明 -->
        <div class="px-4 py-4">
            <div class="privacy-info p-4 mb-6">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                        <i class="fas fa-mask text-white"></i>
                    </div>
                    <h2 class="text-lg font-semibold text-purple-400">隐私浏览已开启</h2>
                </div>
                <p class="text-sm text-gray-400 mb-2">在隐私浏览模式下：</p>
                <ul class="text-sm text-gray-400 space-y-2 ml-5">
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mr-2 mt-1"></i>
                        <span>不会保存您的浏览历史记录</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mr-2 mt-1"></i>
                        <span>不会保存Cookie和网站数据</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-purple-400 mr-2 mt-1"></i>
                        <span>不会保存您在表单中输入的信息</span>
                    </li>
                </ul>
                <p class="text-xs text-gray-500 mt-3">注意：您的互联网服务提供商仍然可以看到您访问的网站</p>
            </div>
        </div>
        
        <!-- 快捷访问 -->
        <div class="px-4 py-2">
            <h2 class="text-base font-semibold text-gray-400 mb-4">快捷访问</h2>
            <div class="shortcuts-grid">
                <!-- 百度 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon">
                        <span class="text-white font-bold">百</span>
                    </div>
                    <div class="shortcut-label">百度</div>
                </div>
                
                <!-- 微博 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon">
                        <i class="fab fa-weibo"></i>
                    </div>
                    <div class="shortcut-label">微博</div>
                </div>
                
                <!-- 知乎 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon">
                        <span class="text-white font-bold">知</span>
                    </div>
                    <div class="shortcut-label">知乎</div>
                </div>
                
                <!-- 添加 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon">
                        <i class="fas fa-plus text-gray-400"></i>
                    </div>
                    <div class="shortcut-label">添加</div>
                </div>
            </div>
        </div>
        
        <!-- 退出隐私浏览 -->
        <div class="px-4 py-6">
            <button class="w-full py-3 bg-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-sign-out-alt text-white mr-2"></i>
                <span class="text-white font-medium">退出隐私浏览模式</span>
            </button>
        </div>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-home"></i></div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-layer-group"></i></div>
            <div class="tab-label">标签页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-bookmark"></i></div>
            <div class="tab-label">书签</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-history"></i></div>
            <div class="tab-label">历史</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-cog"></i></div>
            <div class="tab-label">设置</div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 