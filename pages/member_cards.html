<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 会员介绍</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            color: #333;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .nav-bar {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background-color: #fff;
            z-index: 100;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-back {
            position: absolute;
            left: 16px;
            color: #007aff;
        }
        
        .nav-restore {
            position: absolute;
            right: 16px;
            color: #007aff;
            font-size: 14px;
        }
        
        .header-section {
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header-title {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 6px;
            position: relative;
            z-index: 1;
        }
        
        .header-desc {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        
        .price-badge {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 10px 24px;
            border-radius: 30px;
            font-size: 20px;
            font-weight: 700;
            display: inline-block;
            position: relative;
            z-index: 1;
        }
        
        .content-section {
            padding: 20px;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: 100%;
        }
        
        .feature-card:active {
            transform: scale(0.98);
        }
        
        .feature-card.highlight {
            grid-column: span 2;
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
        }
        
        .feature-card.highlight .feature-title,
        .feature-card.highlight .feature-desc {
            color: white;
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            margin-bottom: 16px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .feature-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: #1f2937;
        }
        
        .feature-desc {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .hot-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #FF6B6B, #FF9F43);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 3px 10px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(255, 107, 107, 0.3);
            z-index: 1;
        }
        
        .benefits-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 100px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1f2937;
            text-align: center;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .benefit-item {
            background-color: #f9fafb;
            padding: 12px;
            border-radius: 12px;
            display: flex;
            align-items: center;
        }
        
        .benefit-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background-color: rgba(55, 81, 255, 0.1);
            color: #3751FF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .benefit-text {
            font-size: 14px;
            font-weight: 500;
            color: #4b5563;
        }
        
        .cta-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px 20px;
            background-color: white;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }
        
        .btn-purchase {
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            text-align: center;
            box-shadow: 0 4px 12px rgba(55, 81, 255, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn-purchase:active {
            transform: scale(0.98);
        }
        
        .content-wrapper {
            padding-bottom: 80px;
        }
        
        .feature-card::after {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(var(--feature-color-rgb), 0.08) 0%, rgba(var(--feature-color-rgb), 0) 70%);
            bottom: -50px;
            right: -50px;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">会员介绍</div>
        <div class="nav-restore">恢复购买</div>
    </div>
    
    <div class="content-wrapper">
        <div class="content-section">
            <!-- 功能卡片网格 -->
            <div class="cards-grid">
                <!-- 功能1：无广告体验 (突出显示) -->
                <div class="feature-card highlight" style="--feature-color-rgb: 255, 255, 255">
                    <div class="hot-tag">热门</div>
                    <div class="feature-icon" style="background-color: rgba(255, 255, 255, 0.2);">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="feature-title">永久无广告</div>
                    <div class="feature-desc">专属会员体验，浏览网页再无广告打扰，让您的阅读体验更加纯净</div>
                </div>
                
                <!-- 功能2：手动标记 -->
                <div class="feature-card" style="--feature-color-rgb: 249, 115, 22">
                    <div class="feature-icon" style="background-color: #f97316;">
                        <i class="fas fa-highlighter"></i>
                    </div>
                    <div class="feature-title">手动标记</div>
                    <div class="feature-desc">轻松标记网页元素，一键去除不想看到的内容</div>
                </div>
                
                <!-- 功能3：双引擎翻译 -->
                <div class="feature-card" style="--feature-color-rgb: 59, 130, 246">
                    <div class="feature-icon" style="background-color: #3b82f6;">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="feature-title">双引擎翻译</div>
                    <div class="feature-desc">网页一键翻译，支持多种翻译引擎和语言</div>
                </div>
                
                <!-- 功能4：油猴增强 -->
                <div class="feature-card" style="--feature-color-rgb: 139, 92, 246">
                    <div class="hot-tag">高级</div>
                    <div class="feature-icon" style="background-color: #8b5cf6;">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="feature-title">油猴增强</div>
                    <div class="feature-desc">支持脚本更新与黑白名单等高级功能</div>
                </div>
                
                <!-- 功能5：自动翻页 -->
                <div class="feature-card" style="--feature-color-rgb: 168, 85, 247">
                    <div class="feature-icon" style="background-color: #a855f7;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="feature-title">自动翻页</div>
                    <div class="feature-desc">网页自动翻页和智能拼页等高级特性</div>
                </div>
                
                <!-- 功能6：iCloud同步 -->
                <div class="feature-card" style="--feature-color-rgb: 14, 165, 233">
                    <div class="feature-icon" style="background-color: #0ea5e9;">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="feature-title">iCloud同步</div>
                    <div class="feature-desc">支持书签、脚本和标记等数据备份</div>
                </div>
                
                <!-- 功能7：多平台支持 -->
                <div class="feature-card" style="--feature-color-rgb: 16, 185, 129">
                    <div class="feature-icon" style="background-color: #10b981;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-title">多平台支持</div>
                    <div class="feature-desc">支持iPhone/iPad多设备同时使用</div>
                </div>
            </div>
            
            <!-- 会员权益 -->
            <div class="benefits-section">
                <div class="section-title">会员特权</div>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <div class="benefit-text">永久使用</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="benefit-text">持续更新</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="benefit-text">数据保护</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="benefit-text">优先客服</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部购买按钮 -->
    <div class="cta-container">
        <button class="btn-purchase">立即购买 · ¥38.00</button>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加按钮点击效果
            const purchaseBtn = document.querySelector('.btn-purchase');
            purchaseBtn.addEventListener('click', function() {
                this.style.transform = 'scale(0.97)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
            
            // 添加卡片点击效果
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html> 