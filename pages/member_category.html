<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 会员介绍</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            color: #333;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .nav-bar {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background-color: #fff;
            z-index: 100;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-back {
            position: absolute;
            left: 16px;
            color: #007aff;
        }
        
        .nav-restore {
            position: absolute;
            right: 16px;
            color: #007aff;
            font-size: 14px;
        }
        
        .banner-section {
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .banner-wave {
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 20px;
        }
        
        .wave-path {
            fill: #f5f7fa;
        }
        
        .banner-title {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 6px;
            position: relative;
            z-index: 1;
        }
        
        .banner-desc {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        
        .price-badge {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 10px 24px;
            border-radius: 30px;
            font-size: 20px;
            font-weight: 700;
            display: inline-block;
            position: relative;
            z-index: 1;
        }
        
        .content-section {
            padding: 20px;
        }
        
        .category-block {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
            padding: 20px;
            margin-bottom: 24px;
        }
        
        .category-header {
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .category-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-right: 12px;
        }
        
        .category-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .feature-item {
            padding: 16px;
            border-radius: 12px;
            background-color: #f9fafb;
            margin-bottom: 12px;
            display: flex;
            align-items: flex-start;
            transition: all 0.3s ease;
        }
        
        .feature-item:last-child {
            margin-bottom: 0;
        }
        
        .feature-item:active {
            transform: scale(0.98);
            background-color: #f3f4f6;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-right: 14px;
            flex-shrink: 0;
        }
        
        .feature-content {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 4px;
            color: #1f2937;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }
        
        .tag {
            background-color: #e0e7ff;
            color: #4f46e5;
            padding: 2px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .cta-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px 20px;
            background-color: white;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }
        
        .price-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 14px;
        }
        
        .price-label {
            font-size: 14px;
            color: #6b7280;
        }
        
        .price-value {
            font-size: 22px;
            font-weight: 700;
            color: #1f2937;
        }
        
        .btn-purchase {
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            text-align: center;
            box-shadow: 0 4px 12px rgba(55, 81, 255, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn-purchase:active {
            transform: scale(0.98);
        }
        
        .benefit-section {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 8px;
        }
        
        .benefit-item {
            background-color: #f9fafb;
            padding: 10px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            flex: 1 0 calc(50% - 10px);
            min-width: calc(50% - 10px);
        }
        
        .benefit-icon {
            width: 28px;
            height: 28px;
            border-radius: 8px;
            background-color: rgba(55, 81, 255, 0.1);
            color: #3751FF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .benefit-text {
            font-size: 13px;
            font-weight: 500;
            color: #4b5563;
        }
        
        .content-wrapper {
            padding-bottom: 130px;
        }
        
        .hot-tag {
            position: absolute;
            top: -6px;
            right: 10px;
            background: linear-gradient(135deg, #FF6B6B, #FF9F43);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 10px;
            z-index: 1;
        }
        
        .feature-item {
            position: relative;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">会员介绍</div>
        <div class="nav-restore">恢复购买</div>
    </div>
    
    <!-- 顶部横幅 -->

    
    <div class="content-wrapper">
        <div class="content-section">
            <!-- 广告体验类别 -->
            <div class="category-block">
                <div class="category-header">
                    <div class="category-icon" style="background-color: #ef4444;">
                        <i class="fas fa-eye-slash"></i>
                    </div>
                    <div class="category-title">广告体验</div>
                </div>
                <div class="feature-item">
                    <div class="hot-tag">热门</div>
                    <div class="feature-icon" style="background-color: #ef4444;">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">永久无广告</div>
                        <div class="feature-desc">专属会员体验，浏览网页再无广告打扰</div>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #f97316;">
                        <i class="fas fa-highlighter"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">手动标记</div>
                        <div class="feature-desc">轻松标记网页元素，一键去除不想看到的内容</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能增强类别 -->
            <div class="category-block">
                <div class="category-header">
                    <div class="category-icon" style="background-color: #8b5cf6;">
                        <i class="fas fa-wand-magic-sparkles"></i>
                    </div>
                    <div class="category-title">功能增强</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #3b82f6;">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">双引擎翻译</div>
                        <div class="feature-desc">网页一键翻译，支持多种翻译引擎和语言</div>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="hot-tag">高级</div>
                    <div class="feature-icon" style="background-color: #8b5cf6;">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">油猴增强</div>
                        <div class="feature-desc">支持脚本更新与黑白名单等高级功能</div>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #a855f7;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">自动翻页</div>
                        <div class="feature-desc">网页自动翻页和智能拼页等高级特性</div>
                    </div>
                </div>
            </div>
            
            <!-- 数据同步类别 -->
            <div class="category-block">
                <div class="category-header">
                    <div class="category-icon" style="background-color: #0ea5e9;">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="category-title">数据同步</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #0ea5e9;">
                        <i class="fas fa-cloud-arrow-up"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">iCloud同步</div>
                        <div class="feature-desc">支持书签、脚本和标记等数据备份</div>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #10b981;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">多平台支持</div>
                        <div class="feature-desc">支持iPhone/iPad多设备同时使用</div>
                    </div>
                </div>
            </div>
            
            <!-- 会员权益 -->
            <div class="category-block">
                <div class="category-header">
                    <div class="category-icon" style="background-color: #f59e0b;">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="category-title">会员权益</div>
                </div>
                <div class="benefit-section">
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-infinity"></i>
                        </div>
                        <div class="benefit-text">永久使用</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="benefit-text">持续更新</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="benefit-text">数据保护</div>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="benefit-text">优先客服</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部购买区域 -->
    <div class="cta-container">
        <div class="price-info">
            <div class="price-label">会员价格</div>
            <div class="price-value">¥38.00</div>
        </div>
        <button class="btn-purchase">立即开通，永久激活</button>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加按钮点击效果
            const purchaseBtn = document.querySelector('.btn-purchase');
            purchaseBtn.addEventListener('click', function() {
                this.style.transform = 'scale(0.97)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
            
            // 添加功能项点击效果
            const featureItems = document.querySelectorAll('.feature-item');
            featureItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    this.style.backgroundColor = '#f3f4f6';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                        this.style.backgroundColor = '#f9fafb';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html> 