<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 工具栏设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
            transition: background-color 0.2s ease;
        }
        .settings-item:hover {
            background-color: rgba(0, 0, 0, 0.01);
        }
        .settings-item:active {
            background-color: rgba(0, 0, 0, 0.02);
        }
        .settings-item:last-child {
            border-bottom: none;
        }
        
        /* 设置图标样式优化 */
        .settings-icon {
            width: 32px;
            height: 32px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
            color: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }
        .settings-item:hover .settings-icon {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
        }
        
        /* 导航栏样式 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        /* 内容区域样式 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        /* 分组标题样式 */
        .group-title {
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #8e8e93;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 4px;
        }
        
        /* 工具栏按钮容器 */
        .toolbar-buttons {
            display: flex;
            justify-content: space-between;
            padding: 15px;
            background-color: #fff;
            border-radius: 14px;
            margin-bottom: 20px;
        }
        
        /* 工具栏按钮样式 */
        .toolbar-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 18%;
            text-align: center;
            position: relative;
        }
        
        .button-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            background-color: #f2f2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            font-size: 20px;
            color: #007aff;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .button-icon:active {
            transform: scale(0.95);
            background-color: #e5e5ea;
        }
        
        .button-label {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }
        
        .toolbar-button.active .button-icon {
            background-color: #e1f0ff;
            color: #007aff;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
        }
        
        /* 手势列表样式 */
        .gesture-list {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .gesture-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 14px 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
        }
        
        .gesture-info {
            display: flex;
            align-items: center;
        }
        
        .gesture-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background-color: #f2f2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #333;
        }
        
        .gesture-name {
            font-weight: 500;
        }
        
        .gesture-actions {
            display: flex;
            gap: 8px;
        }
        
        .gesture-action-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f2f2f7;
            color: #333;
            transition: all 0.2s ease;
        }
        
        .gesture-action-btn:active {
            transform: scale(0.92);
            background-color: #e5e5ea;
        }
        
        .gesture-action-btn.delete {
            color: #ff3b30;
        }
        
        .add-gesture-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 14px 16px;
            color: #007aff;
            font-weight: 500;
        }
        
        .add-gesture-btn i {
            margin-right: 8px;
        }
        
        .empty-state {
            padding: 32px 16px;
            text-align: center;
            color: #8e8e93;
        }
        
        .empty-icon {
            font-size: 40px;
            margin-bottom: 12px;
            opacity: 0.5;
        }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back" onclick="history.back()">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">工具栏设置</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container px-4">
            <p class="text-sm text-gray-500 mb-4 px-1">设置工具栏按钮长按手势触发的操作</p>
            
            <!-- 工具栏按钮选择 -->
            <div class="toolbar-buttons">
                <div class="toolbar-button active" data-button="backward">
                    <div class="button-icon">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <div class="button-label">后退</div>
                </div>
                
                <div class="toolbar-button" data-button="forward">
                    <div class="button-icon">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                    <div class="button-label">前进</div>
                </div>
                
                <div class="toolbar-button" data-button="new">
                    <div class="button-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="button-label">新建</div>
                </div>
                
                <div class="toolbar-button" data-button="tabs">
                    <div class="button-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="button-label">标签页</div>
                </div>
                
                <div class="toolbar-button" data-button="functions">
                    <div class="button-icon">
                        <i class="fas fa-ellipsis"></i>
                    </div>
                    <div class="button-label">功能</div>
                </div>
            </div>
            
            <!-- 手势管理区 - 后退按钮（默认显示） -->
            <div id="backward-gestures" class="gesture-section">
                <div class="group-title px-2 mt-6 mb-2">长按后退按钮触发的操作</div>
                
                <div class="gesture-list">
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-clock-rotate-left"></i>
                            </div>
                            <div class="gesture-name">返回历史记录</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="gesture-name">回到首页</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="add-gesture-btn">
                        <i class="fas fa-plus"></i> 添加手势操作
                    </div>
                </div>
            </div>
            
            <!-- 手势管理区 - 前进按钮（隐藏） -->
            <div id="forward-gestures" class="gesture-section hidden">
                <div class="group-title px-2 mt-6 mb-2">长按前进按钮触发的操作</div>
                
                <div class="gesture-list">
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-clock-rotate-left"></i>
                            </div>
                            <div class="gesture-name">前进历史记录</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="add-gesture-btn">
                        <i class="fas fa-plus"></i> 添加手势操作
                    </div>
                </div>
            </div>
            
            <!-- 手势管理区 - 新建按钮（隐藏） -->
            <div id="new-gestures" class="gesture-section hidden">
                <div class="group-title px-2 mt-6 mb-2">长按新建按钮触发的操作</div>
                
                <div class="gesture-list">
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-eye-slash"></i>
                            </div>
                            <div class="gesture-name">新建隐私标签页</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-clone"></i>
                            </div>
                            <div class="gesture-name">复制当前标签页</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="add-gesture-btn">
                        <i class="fas fa-plus"></i> 添加手势操作
                    </div>
                </div>
            </div>
            
            <!-- 手势管理区 - 标签页按钮（隐藏） -->
            <div id="tabs-gestures" class="gesture-section hidden">
                <div class="group-title px-2 mt-6 mb-2">长按标签页按钮触发的操作</div>
                
                <div class="gesture-list">
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-hand-point-up"></i>
                        </div>
                        <p>暂无长按手势配置</p>
                        <p class="text-xs mt-2">点击下方按钮添加手势</p>
                    </div>
                    <div class="add-gesture-btn">
                        <i class="fas fa-plus"></i> 添加手势操作
                    </div>
                </div>
            </div>
            
            <!-- 手势管理区 - 功能按钮（隐藏） -->
            <div id="functions-gestures" class="gesture-section hidden">
                <div class="group-title px-2 mt-6 mb-2">长按功能按钮触发的操作</div>
                
                <div class="gesture-list">
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-share-nodes"></i>
                            </div>
                            <div class="gesture-name">分享页面</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-bookmark"></i>
                            </div>
                            <div class="gesture-name">添加书签</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="gesture-item">
                        <div class="gesture-info">
                            <div class="gesture-icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="gesture-name">下载管理</div>
                        </div>
                        <div class="gesture-actions">
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="gesture-action-btn">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="gesture-action-btn delete">
                                <i class="fas fa-trash-can"></i>
                            </div>
                        </div>
                    </div>
                    <div class="add-gesture-btn">
                        <i class="fas fa-plus"></i> 添加手势操作
                    </div>
                </div>
            </div>
            
            <!-- 提示信息 -->
            <div class="text-xs text-gray-500 mt-4 px-2">
                <p>提示：长按对应的工具栏按钮可以快速触发常用操作</p>
                <p class="mt-1">可以通过拖动排序调整长按后显示的操作顺序</p>
            </div>
        </div>
    </div>
    
    <script>
        // 工具栏按钮切换功能
        document.querySelectorAll('.toolbar-button').forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有活跃状态
                document.querySelectorAll('.toolbar-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // 隐藏所有手势区域
                document.querySelectorAll('.gesture-section').forEach(section => {
                    section.classList.add('hidden');
                });
                
                // 添加当前按钮活跃状态
                button.classList.add('active');
                
                // 显示对应的手势区域
                const buttonType = button.getAttribute('data-button');
                document.getElementById(`${buttonType}-gestures`).classList.remove('hidden');
            });
        });
        
        // 测试用：删除手势功能
        document.querySelectorAll('.gesture-action-btn.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const gestureItem = e.target.closest('.gesture-item');
                gestureItem.style.height = gestureItem.offsetHeight + 'px';
                gestureItem.style.overflow = 'hidden';
                setTimeout(() => {
                    gestureItem.style.height = '0';
                    gestureItem.style.padding = '0';
                    gestureItem.style.margin = '0';
                    gestureItem.style.opacity = '0';
                }, 10);
                setTimeout(() => {
                    gestureItem.remove();
                    checkEmptyState();
                }, 300);
            });
        });
        
        // 检查空状态函数
        function checkEmptyState() {
            document.querySelectorAll('.gesture-section').forEach(section => {
                const items = section.querySelectorAll('.gesture-item');
                const emptyState = section.querySelector('.empty-state');
                
                if (items.length === 0 && !emptyState) {
                    const gestureList = section.querySelector('.gesture-list');
                    const addButton = gestureList.querySelector('.add-gesture-btn');
                    
                    const emptyStateHtml = `
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-hand-point-up"></i>
                            </div>
                            <p>暂无长按手势配置</p>
                            <p class="text-xs mt-2">点击下方按钮添加手势</p>
                        </div>
                    `;
                    
                    gestureList.insertAdjacentHTML('afterbegin', emptyStateHtml);
                }
            });
        }
    </script>
</body>
</html> 