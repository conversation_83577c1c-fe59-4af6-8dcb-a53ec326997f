<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览Google</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* 自定义样式 */
        .search-bar {
            border-radius: 24px;
            border: 1px solid #dfe1e5;
            box-shadow: 0 1px 6px rgba(32, 33, 36, 0.16);
            transition: all 0.3s ease;
            font-size: 12px;
            height: 32px;
        }

        .search-bar:focus-within {
            box-shadow: 0 1px 12px rgba(32, 33, 36, 0.28);
            border-color: transparent;
        }

        .google-results {
            font-family: Arial, sans-serif;
        }

        .search-result {
            cursor: pointer;
            transition: all 0.2s;
        }

        .search-result:hover {
            background-color: #f8f9fa;
        }

        .circle-button {
            width: 32px;
            height: 32px;
            min-width: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .circle-button:hover {
            background-color: #f1f3f4;
        }

        /* 搜索建议下拉框 */
        .search-suggestions {
            border-radius: 0 0 24px 24px;
            box-shadow: 0 4px 6px rgba(32, 33, 36, 0.16);
            border: 1px solid #dfe1e5;
            border-top: none;
        }

        /* 菜单下拉项 */
        .menu-dropdown {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            right: 0;
            left: auto;
            width: 180px;
        }

        .menu-item {
            transition: background-color 0.2s;
        }

        .menu-item:hover {
            background-color: #f1f3f4;
        }

        /* 适配移动端 */
        @media (max-width: 375px) {
            .browser-top-bar {
                padding-left: 2px;
                padding-right: 2px;
            }

            .circle-button {
                width: 28px;
                height: 28px;
                min-width: 28px;
            }
        }

        /* 阅读模式按钮样式 */
        .reading-mode-button {
            position: fixed;
            bottom: 80px;
            right: 0;
            display: flex;
            align-items: center;
            z-index: 100;
            transition: all 0.3s ease;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.08);
            border-radius: 18px 0 0 18px;
            overflow: hidden;
            height: 36px; /* 整体高度为36px */
            width: 48px; /* 调整整体宽度为48px */
        }

        .reading-mode-button:active {
            transform: scale(0.95);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
        }

        /* 左侧半圆部分 */
        .reading-mode-left {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 左侧圆形图标 */
        .reading-mode-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #4285f4;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-left: -2px; /* 微调图标位置，使其更居中 */
        }

        /* 右侧部分 */
        .reading-mode-right {
            height: 36px;
            width: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 阅读模式提示 */
        .reading-mode-tooltip {
            position: absolute;
            bottom: 46px;
            right: 0;
            background-color: white;
            color: #333;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12), 0 1px 3px rgba(0, 0, 0, 0.08);
            opacity: 0;
            transform: translateY(8px);
            transition: all 0.3s ease;
            pointer-events: none;
            right: -4px; /* 微调提示框位置 */
        }

        .reading-mode-button:hover .reading-mode-tooltip {
            opacity: 1;
            transform: translateY(0);
        }

        /* 阅读模式配置弹窗样式 - iOS Popover风格 */
        .reading-settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .reading-settings-overlay.hidden {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
        }

        .reading-settings-popup {
            position: absolute;
            top: 60px;
            right: 8px;
            background-color: white;
            border-radius: 16px;
            width: 280px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: scale(0.8) translateY(-10px);
            transform-origin: top right;
            transition: all 0.25s cubic-bezier(0.34, 1.56, 0.64, 1);
            opacity: 0;
        }

        .reading-settings-overlay:not(.hidden) .reading-settings-popup {
            transform: scale(1) translateY(0);
            opacity: 1;
        }

        /* Popover箭头 */
        .reading-settings-popup::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 20px;
            width: 16px;
            height: 16px;
            background-color: white;
            transform: rotate(45deg);
            box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
        }

        /* 弹窗头部 */
        .popup-header {
            padding: 16px 20px 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .popup-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
            text-align: center;
        }

        /* 弹窗内容 */
        .popup-content {
            padding: 16px 20px;
        }

        .setting-section {
            margin-bottom: 24px;
        }

        .setting-section:last-child {
            margin-bottom: 0;
        }

        .setting-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        /* 设置区域头部 */
        .setting-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .setting-header .setting-label {
            margin-bottom: 0;
        }

        /* 退出阅读模式按钮 */
        .exit-reading-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            border: 1px solid #e8e8e8;
            background-color: #ffffff;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .exit-reading-btn:hover {
            background-color: #f8f9ff;
            border-color: #4285f4;
            color: #4285f4;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(66, 133, 244, 0.15);
        }

        .exit-reading-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 4px rgba(66, 133, 244, 0.2);
        }

        .exit-reading-btn i {
            font-size: 10px;
        }

        .exit-reading-btn span {
            font-size: 11px;
        }

        /* 字体大小控制 */
        .font-size-control {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 8px 12px;
        }

        .font-size-btn {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 50%;
            background-color: white;
            color: #4285f4;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 18px;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .font-size-btn:hover {
            background-color: #4285f4;
            color: white;
            transform: scale(1.05);
        }

        .font-size-btn:active {
            transform: scale(0.95);
        }

        .font-size-btn:disabled {
            background-color: #e8e8e8;
            color: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .font-size-display {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            min-width: 60px;
            text-align: center;
        }

        /* 背景颜色选项 */
        .background-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .bg-option {
            height: 50px;
            border: 2px solid #e8e8e8;
            border-radius: 10px;
            background-color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 4px;
        }

        .bg-option:hover {
            border-color: #4285f4;
            transform: translateY(-1px);
        }

        .bg-option.active {
            border-color: #4285f4;
            background-color: #f8f9ff;
        }

        .bg-preview {
            width: 20px;
            height: 12px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }

        .bg-white-preview { background-color: #ffffff; }
        .bg-beige-preview { background-color: #f5f5dc; }
        .bg-dark-preview { background-color: #2d3748; }
        .bg-green-preview { background-color: #c6f6d5; }
        .bg-blue-preview { background-color: #e6f3ff; }
        .bg-pink-preview { background-color: #fce7f3; }
        .bg-yellow-preview { background-color: #fef3c7; }
        .bg-gray-preview { background-color: #f3f4f6; }
        .bg-purple-preview { background-color: #ede9fe; }

        .bg-option span {
            font-size: 10px;
            color: #666;
            font-weight: 500;
        }

        .bg-option.active span {
            color: #4285f4;
            font-weight: 600;
        }



        /* 背景主题样式 */
        .theme-white { background-color: #ffffff !important; color: #333 !important; }
        .theme-beige { background-color: #f5f5dc !important; color: #333 !important; }
        .theme-dark { background-color: #2d3748 !important; color: #e2e8f0 !important; }
        .theme-green { background-color: #c6f6d5 !important; color: #2d3748 !important; }
        .theme-blue { background-color: #e6f3ff !important; color: #1e40af !important; }
        .theme-pink { background-color: #fce7f3 !important; color: #be185d !important; }
        .theme-yellow { background-color: #fef3c7 !important; color: #92400e !important; }
        .theme-gray { background-color: #f3f4f6 !important; color: #374151 !important; }
        .theme-purple { background-color: #ede9fe !important; color: #6b21a8 !important; }

        /* 字体大小样式 */
        .font-size-12 { font-size: 12px !important; }
        .font-size-13 { font-size: 13px !important; }
        .font-size-14 { font-size: 14px !important; }
        .font-size-15 { font-size: 15px !important; }
        .font-size-16 { font-size: 16px !important; }
        .font-size-17 { font-size: 17px !important; }
        .font-size-18 { font-size: 18px !important; }
        .font-size-19 { font-size: 19px !important; }
        .font-size-20 { font-size: 20px !important; }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>

    <!-- 浏览器顶部搜索栏 -->
    <div class="browser-top-bar bg-white px-2 pt-14 pb-2 sticky top-0 z-10 border-b border-gray-200">
        <div class="flex items-center gap-1">
            <!-- 脚本入口 -->
            <div class="circle-button" title="运行脚本" style="min-width: 32px; width: 32px; height: 32px;">
                <i class="fas fa-code text-gray-600 text-sm"></i>
            </div>

            <!-- 搜索框 -->
            <div class="search-bar flex items-center flex-1 px-2 py-1 relative">
                <!-- 网站图标 -->
                <img src="https://www.google.com/favicon.ico" class="w-4 h-4 mr-1" alt="Google">

                <!-- 显示当前URL/搜索内容 -->
                <div class="flex-1 truncate text-xs">
                    <span class="text-gray-600">google.com/search?q=</span><span class="text-black font-medium">modern web</span>
                </div>

                <!-- 小星标/书签 -->
                <button class="ml-1 text-gray-400 hover:text-yellow-500">
                    <i class="far fa-star text-xs"></i>
                </button>
            </div>

            <!-- 刷新按钮 -->
            <div class="circle-button" title="刷新页面" style="min-width: 32px; width: 32px; height: 32px;">
                <i class="fas fa-redo-alt text-gray-600 text-sm"></i>
            </div>

            <!-- 菜单按钮 -->
            <div class="circle-button relative" title="更多选项" style="min-width: 32px; width: 32px; height: 32px;" id="menuButton">
                <i class="fas fa-ellipsis-v text-gray-600 text-sm"></i>

                <!-- 菜单下拉框 (默认隐藏) -->
                <div class="menu-dropdown absolute right-0 top-full mt-1 bg-white hidden z-20" id="menuDropdown">
                    <div class="py-2 px-1">
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center">
                            <i class="fas fa-bookmark mr-2 text-gray-600"></i>
                            <span>添加书签</span>
                        </div>
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center">
                            <i class="fas fa-share-alt mr-2 text-gray-600"></i>
                            <span>分享页面</span>
                        </div>
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center">
                            <i class="fas fa-search-plus mr-2 text-gray-600"></i>
                            <span>查找页面内容</span>
                        </div>
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center" id="readingSettingsItem">
                            <i class="fas fa-book-reader mr-2 text-gray-600"></i>
                            <span>阅读模式设置</span>
                        </div>
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center">
                            <i class="fas fa-desktop mr-2 text-gray-600"></i>
                            <span>请求桌面版</span>
                        </div>
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center">
                            <i class="fas fa-info-circle mr-2 text-gray-600"></i>
                            <span>网站信息</span>
                        </div>
                        <div class="menu-item px-2 py-2 text-xs rounded-md flex items-center">
                            <i class="fas fa-download mr-2 text-gray-600"></i>
                            <span>下载页面</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索建议下拉框 (模拟，默认隐藏) -->
        <div class="search-suggestions hidden">
            <div class="px-4 py-2 border-b border-gray-100 flex items-center text-xs">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <span>现代网页设计趋势</span>
            </div>
            <div class="px-4 py-2 border-b border-gray-100 flex items-center text-xs">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <span>现代网页设计趋势</span>
            </div>
            <div class="px-4 py-2 border-b border-gray-100 flex items-center text-xs">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <span>现代网页设计趋势</span>
            </div>
            <div class="px-4 py-2 flex items-center text-xs">
                <i class="fas fa-search text-gray-400 mr-3"></i>
                <span>现代网页设计趋势</span>
            </div>
        </div>
    </div>

    <!-- Google 搜索结果内容区域 -->
    <div class="content-area google-results pt-2 pb-20 h-full overflow-y-auto">
        <!-- Google Logo -->
        <div class="flex justify-center mt-2 mb-6">
            <img src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png" alt="Google" class="h-7">
        </div>

        <!-- 搜索信息栏 -->
        <div class="text-sm text-gray-600 px-4 pb-2 border-b border-gray-200">
            约 1,760,000,000 条结果 (0.48 秒)
        </div>

        <!-- 搜索结果 -->
        <div class="search-results px-4 py-3">
            <!-- 结果 1 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.example.com › modern-web-design</div>
                <div class="text-blue-700 text-lg font-medium mb-1">2023现代网页设计趋势与最佳实践 - Example</div>
                <div class="text-sm text-gray-800">现代网页设计强调简洁性、响应式布局和用户体验。了解最新的设计趋势，包括深色模式、微交互和沉浸式3D效果...</div>
            </div>

            <!-- 结果 2 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.webdesigntrends.com</div>
                <div class="text-blue-700 text-lg font-medium mb-1">网页设计的10大现代趋势 | Web Design Trends</div>
                <div class="text-sm text-gray-800">发现2023年主导网页设计的关键趋势。从新拟态UI到声音设计，了解当今最流行的设计方向...</div>
            </div>

            <!-- 结果 3 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.designacademy.edu/blog</div>
                <div class="text-blue-700 text-lg font-medium mb-1">如何掌握现代网页设计的基础 - Design Academy</div>
                <div class="text-sm text-gray-800">现代网页设计结合了美学和功能性。本指南介绍了核心原则，包括排版、色彩理论、空间利用和视觉层次结构...</div>
            </div>

            <!-- 结果 4 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.creativestudio.com/resources</div>
                <div class="text-blue-700 text-lg font-medium mb-1">30个令人惊艳的现代网页设计案例 - Creative Studio</div>
                <div class="text-sm text-gray-800">从全球顶尖品牌和创意工作室中精选的30个杰出网站设计。每个案例都包含详细分析和关键设计要素解释...</div>
            </div>

            <!-- 结果 5 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.techinsider.io/web-design</div>
                <div class="text-blue-700 text-lg font-medium mb-1">现代网页设计必备的5款设计工具 - Tech Insider</div>
                <div class="text-sm text-gray-800">探索专业设计师推荐的顶级网页设计工具。从Figma到Adobe XD，这些工具将帮助你创建优雅且用户友好的网站...</div>
            </div>

            <!-- 相关搜索 -->
            <div class="related-searches mt-8">
                <div class="text-lg text-gray-800 mb-3">相关搜索</div>
                <div class="grid grid-cols-2 gap-2">
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">现代网页设计课程</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">网页设计最新技术</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">UI/UX 设计趋势</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">前端开发框架比较</div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination mt-8 flex justify-center items-center">
                <div class="text-2xl text-blue-500 font-bold mr-4">G</div>
                <div class="text-blue-500">1</div>
                <div class="text-blue-500 mx-3">2</div>
                <div class="text-blue-500 mx-3">3</div>
                <div class="text-blue-500 mx-3">4</div>
                <div class="text-blue-500 mx-3">5</div>
                <div class="text-blue-500 mx-3">6</div>
                <div class="text-blue-500 mx-3">7</div>
                <div class="text-blue-500 mx-3">8</div>
                <div class="text-blue-500 mx-3">9</div>
                <div class="text-blue-500 mx-3">10</div>
                <div class="text-blue-500 ml-4">下一页</div>
            </div>
        </div>
    </div>

    <!-- 阅读模式配置弹窗 -->
    <div class="reading-settings-overlay hidden" id="readingSettingsOverlay">
        <div class="reading-settings-popup" id="readingSettingsPopup">
            <!-- 弹窗内容 -->
            <div class="popup-content">
                <!-- 字体大小设置 -->
                <div class="setting-section">
                    <div class="setting-header">
                        <div class="setting-label">字体大小</div>
                        <button class="exit-reading-btn" id="closeReadingSettings">
                            <i class="fas fa-times"></i>
                            <span>退出阅读模式</span>
                        </button>
                    </div>
                    <div class="font-size-control">
                        <button class="font-size-btn" id="decreaseFontSize">−</button>
                        <div class="font-size-display" id="fontSizeDisplay">14px</div>
                        <button class="font-size-btn" id="increaseFontSize">+</button>
                    </div>
                </div>

                <!-- 背景颜色设置 -->
                <div class="setting-section">
                    <div class="setting-label">背景颜色</div>
                    <div class="background-options">
                        <button class="bg-option active" data-bg="white">
                            <div class="bg-preview bg-white-preview"></div>
                            <span>白色</span>
                        </button>
                        <button class="bg-option" data-bg="beige">
                            <div class="bg-preview bg-beige-preview"></div>
                            <span>米色</span>
                        </button>
                        <button class="bg-option" data-bg="dark">
                            <div class="bg-preview bg-dark-preview"></div>
                            <span>深色</span>
                        </button>
                        <button class="bg-option" data-bg="green">
                            <div class="bg-preview bg-green-preview"></div>
                            <span>护眼绿</span>
                        </button>
                        <button class="bg-option" data-bg="blue">
                            <div class="bg-preview bg-blue-preview"></div>
                            <span>护眼蓝</span>
                        </button>
                        <button class="bg-option" data-bg="pink">
                            <div class="bg-preview bg-pink-preview"></div>
                            <span>粉色</span>
                        </button>
                        <button class="bg-option" data-bg="yellow">
                            <div class="bg-preview bg-yellow-preview"></div>
                            <span>暖黄</span>
                        </button>
                        <button class="bg-option" data-bg="gray">
                            <div class="bg-preview bg-gray-preview"></div>
                            <span>灰色</span>
                        </button>
                        <button class="bg-option" data-bg="purple">
                            <div class="bg-preview bg-purple-preview"></div>
                            <span>紫色</span>
                        </button>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 阅读模式按钮 -->
    <div class="reading-mode-button" id="readingModeBtn">
        <div class="reading-mode-left">
            <div class="reading-mode-icon">
                <i class="fas fa-book-reader text-sm"></i>
            </div>
        </div>
        <div class="reading-mode-right"></div>
        <div class="reading-mode-tooltip">阅读模式</div>
    </div>

    <!-- 底部标签栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <div class="tab-icon"><i class="fas fa-home"></i></div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-layer-group"></i></div>
            <div class="tab-label">标签页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-bookmark"></i></div>
            <div class="tab-label">书签</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-history"></i></div>
            <div class="tab-label">历史</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-cog"></i></div>
            <div class="tab-label">设置</div>
        </div>
    </div>

    <script src="../js/app.js"></script>
    <script>
        // 阅读模式功能实现
        document.addEventListener('DOMContentLoaded', function() {
            // 阅读模式配置变量
            let currentFontSize = 14; // 字体大小，单位px
            let currentBackground = 'white';

            // 字体大小范围
            const minFontSize = 12;
            const maxFontSize = 20;

            // 菜单按钮和下拉框交互
            const menuButton = document.getElementById('menuButton');
            const menuDropdown = document.getElementById('menuDropdown');
            const readingSettingsItem = document.getElementById('readingSettingsItem');
            const readingSettingsOverlay = document.getElementById('readingSettingsOverlay');
            const closeReadingSettings = document.getElementById('closeReadingSettings');
            const fontSizeDisplay = document.getElementById('fontSizeDisplay');
            const decreaseFontBtn = document.getElementById('decreaseFontSize');
            const increaseFontBtn = document.getElementById('increaseFontSize');

            // 菜单按钮点击事件
            menuButton.addEventListener('click', function(e) {
                e.stopPropagation();
                menuDropdown.classList.toggle('hidden');
            });

            // 点击其他地方关闭菜单
            document.addEventListener('click', function() {
                menuDropdown.classList.add('hidden');
            });

            // 阻止菜单内部点击事件冒泡
            menuDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // 阅读模式设置项点击事件
            readingSettingsItem.addEventListener('click', function() {
                menuDropdown.classList.add('hidden');
                showReadingSettings();
            });

            // 显示阅读模式设置弹窗
            function showReadingSettings() {
                readingSettingsOverlay.classList.remove('hidden');
            }

            // 隐藏阅读模式设置弹窗
            function hideReadingSettings() {
                readingSettingsOverlay.classList.add('hidden');
            }

            // 关闭按钮点击事件
            closeReadingSettings.addEventListener('click', hideReadingSettings);

            // 点击弹窗外区域关闭弹窗
            readingSettingsOverlay.addEventListener('click', function(e) {
                if (e.target === readingSettingsOverlay) {
                    hideReadingSettings();
                }
            });

            // 字体大小控制
            decreaseFontBtn.addEventListener('click', function() {
                if (currentFontSize > minFontSize) {
                    currentFontSize--;
                    updateFontSizeDisplay();
                    applyReadingSettings(); // 立即应用
                }
            });

            increaseFontBtn.addEventListener('click', function() {
                if (currentFontSize < maxFontSize) {
                    currentFontSize++;
                    updateFontSizeDisplay();
                    applyReadingSettings(); // 立即应用
                }
            });

            // 更新字体大小显示
            function updateFontSizeDisplay() {
                fontSizeDisplay.textContent = currentFontSize + 'px';

                // 更新按钮状态
                decreaseFontBtn.disabled = currentFontSize <= minFontSize;
                increaseFontBtn.disabled = currentFontSize >= maxFontSize;
            }

            // 背景颜色选择
            const bgOptions = document.querySelectorAll('.bg-option');
            bgOptions.forEach(option => {
                option.addEventListener('click', function() {
                    bgOptions.forEach(o => o.classList.remove('active'));
                    this.classList.add('active');
                    currentBackground = this.dataset.bg;
                    applyReadingSettings(); // 立即应用
                });
            });



            // 应用阅读模式设置到页面
            function applyReadingSettings() {
                const contentArea = document.querySelector('.content-area');
                const searchResults = document.querySelectorAll('.search-result');

                // 应用背景主题
                contentArea.classList.remove('theme-white', 'theme-beige', 'theme-dark', 'theme-green', 'theme-blue', 'theme-pink', 'theme-yellow', 'theme-gray', 'theme-purple');
                contentArea.classList.add(`theme-${currentBackground}`);

                // 应用字体大小
                searchResults.forEach(result => {
                    // 移除所有字体大小类
                    for (let i = 12; i <= 20; i++) {
                        result.classList.remove(`font-size-${i}`);
                    }
                    result.classList.add(`font-size-${currentFontSize}`);
                });

                // 保存设置到本地存储
                localStorage.setItem('readingFontSize', currentFontSize);
                localStorage.setItem('readingBackground', currentBackground);
            }

            // 加载保存的设置
            function loadSavedSettings() {
                const savedFontSize = localStorage.getItem('readingFontSize');
                const savedBackground = localStorage.getItem('readingBackground');

                if (savedFontSize) {
                    currentFontSize = parseInt(savedFontSize);
                }

                if (savedBackground) {
                    currentBackground = savedBackground;
                    // 更新背景选择状态
                    bgOptions.forEach(option => {
                        option.classList.remove('active');
                        if (option.dataset.bg === savedBackground) {
                            option.classList.add('active');
                        }
                    });
                }

                updateFontSizeDisplay();
            }

            // 初始化设置
            loadSavedSettings();
            // 检测页面是否适合阅读模式
            function checkReadingModeEligibility() {
                // 获取搜索结果内容
                const searchResults = document.querySelectorAll('.search-result');

                // 如果有足够的文本内容，则显示阅读模式按钮
                if (searchResults.length > 2) {
                    document.getElementById('readingModeBtn').style.display = 'flex';
                } else {
                    document.getElementById('readingModeBtn').style.display = 'none';
                }
            }

            // 阅读模式按钮点击事件
            document.getElementById('readingModeBtn').addEventListener('click', function() {
                // 切换到阅读模式
                toggleReadingMode();
            });

            // 切换阅读模式
            function toggleReadingMode() {
                const contentArea = document.querySelector('.content-area');
                const searchResults = document.querySelectorAll('.search-result');

                // 移除非必要元素
                document.querySelector('.browser-top-bar').classList.toggle('hidden');
                document.querySelector('.tab-bar').classList.toggle('hidden');

                // 调整内容区域样式
                contentArea.classList.toggle('reading-mode');
                if (contentArea.classList.contains('reading-mode')) {
                    contentArea.style.height = '100vh';
                    contentArea.style.paddingTop = '20px';

                    // 应用用户设置的背景主题
                    contentArea.classList.remove('theme-white', 'theme-beige', 'theme-dark', 'theme-green');
                    contentArea.classList.add(`theme-${currentBackground}`);

                    // 调整搜索结果样式
                    searchResults.forEach(result => {
                        result.style.maxWidth = '90%';
                        result.style.margin = '0 auto 16px';
                        result.style.padding = '16px';
                        result.style.backgroundColor = 'white';
                        result.style.borderRadius = '12px';
                        result.style.boxShadow = '0 1px 4px rgba(0,0,0,0.1)';

                        // 应用用户设置的字体大小
                        for (let i = 12; i <= 20; i++) {
                            result.classList.remove(`font-size-${i}`);
                        }
                        result.classList.add(`font-size-${currentFontSize}`);
                    });

                    // 更改按钮图标
                    document.querySelector('#readingModeBtn .reading-mode-icon i').classList.remove('fa-book-reader');
                    document.querySelector('#readingModeBtn .reading-mode-icon i').classList.add('fa-times');
                    document.querySelector('#readingModeBtn .reading-mode-icon i').classList.add('text-sm');
                    document.querySelector('.reading-mode-tooltip').textContent = '退出阅读模式';
                } else {
                    contentArea.style.height = '';
                    contentArea.style.paddingTop = '';

                    // 移除主题样式
                    contentArea.classList.remove('theme-white', 'theme-beige', 'theme-dark', 'theme-green', 'theme-blue', 'theme-pink', 'theme-yellow', 'theme-gray', 'theme-purple');

                    // 恢复搜索结果样式
                    searchResults.forEach(result => {
                        result.style.maxWidth = '';
                        result.style.margin = '';
                        result.style.padding = '';
                        result.style.backgroundColor = '';
                        result.style.borderRadius = '';
                        result.style.boxShadow = '';

                        // 移除字体大小样式
                        for (let i = 12; i <= 20; i++) {
                            result.classList.remove(`font-size-${i}`);
                        }
                    });

                    // 恢复按钮图标
                    document.querySelector('#readingModeBtn .reading-mode-icon i').classList.remove('fa-times');
                    document.querySelector('#readingModeBtn .reading-mode-icon i').classList.add('fa-book-reader');
                    document.querySelector('#readingModeBtn .reading-mode-icon i').classList.add('text-sm');
                    document.querySelector('.reading-mode-tooltip').textContent = '阅读模式';
                }
            }

            // 初始检查页面是否适合阅读模式
            checkReadingModeEligibility();
        });
    </script>
</body>
</html>