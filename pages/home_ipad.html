<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - iPad首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            max-width: 1024px;
            margin: 0 auto;
        }
        .search-bar {
            background-color: rgba(142, 142, 147, 0.12);
            border-radius: 12px;
            max-width: 720px;
            margin: 0 auto;
        }
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 24px;
            margin-bottom: 30px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }
        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .shortcut-icon {
            width: 80px;
            height: 80px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 32px;
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .shortcut-label {
            font-size: 14px;
            font-weight: 500;
            text-align: center;
        }
        .shortcuts-empty {
            padding: 50px 0;
            text-align: center;
        }
        .browser-toolbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 12px 20px;
            display: flex;
            justify-content: center;
            z-index: 100;
        }
        .browser-toolbar-inner {
            display: flex;
            justify-content: space-between;
            max-width: 720px;
            width: 100%;
        }
        .browser-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 70px;
        }
        .browser-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
        }
        .browser-label {
            font-size: 12px;
            color: #6b7280;
            white-space: nowrap;
        }
        .browser-icon.active {
            background-color: #3b82f6;
            color: white;
        }
        .section-title {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 24px;
        }
        .recently-visited {
            max-width: 900px;
            margin: 0 auto 30px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 0 24px;
        }
        .recent-item {
            display: flex;
            align-items: center;
            background-color: #f9fafb;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .recent-favicon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            font-size: 18px;
            color: white;
        }
        .recent-info {
            overflow: hidden;
        }
        .recent-title {
            font-weight: 500;
            font-size: 14px;
            color: #1f2937;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .recent-url {
            font-size: 12px;
            color: #6b7280;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        @media (min-width: 768px) {
            .shortcuts-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }
        @media (min-width: 1024px) {
            .shortcuts-grid {
                grid-template-columns: repeat(8, 1fr);
            }
            .recently-visited {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area pt-14 pb-32 h-full overflow-y-auto relative">
        <!-- 搜索栏 -->
        <div class="px-6 py-6">
            <div class="search-bar flex items-center px-4 py-4">
                <i class="fas fa-search text-gray-500 mr-3"></i>
                <input type="text" placeholder="搜索或输入网址" class="bg-transparent w-full outline-none text-lg">
                <i class="fas fa-microphone text-gray-500 ml-3"></i>
            </div>
        </div>
        
        <!-- 快捷访问 -->
        <div class="px-6 py-4">
            <div class="section-title flex justify-between items-center mb-5">
                <h2 class="text-2xl font-semibold">快捷访问</h2>
                <button class="text-blue-500 text-base">编辑</button>
            </div>
            
            <!-- 有快捷访问项的情况 -->
            <div class="shortcuts-grid" id="shortcuts-container">
                <!-- 百度 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #3388FF;">
                        <span class="text-white font-bold">百</span>
                    </div>
                    <div class="shortcut-label">百度</div>
                </div>
                
                <!-- 微博 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #E6162D;">
                        <i class="fab fa-weibo"></i>
                    </div>
                    <div class="shortcut-label">微博</div>
                </div>
                
                <!-- 淘宝 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #FF4400;">
                        <span class="text-white font-bold">淘</span>
                    </div>
                    <div class="shortcut-label">淘宝</div>
                </div>
                
                <!-- 京东 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #E1251B;">
                        <span class="text-white font-bold">京</span>
                    </div>
                    <div class="shortcut-label">京东</div>
                </div>
                
                <!-- 知乎 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #0066FF;">
                        <span class="text-white font-bold">知</span>
                    </div>
                    <div class="shortcut-label">知乎</div>
                </div>
                
                <!-- 哔哩哔哩 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #FB7299;">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="shortcut-label">哔哩哔哩</div>
                </div>
                
                <!-- 微信读书 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #09BB07;">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="shortcut-label">微信读书</div>
                </div>
                
                <!-- 小红书 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #FE2C55;">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <div class="shortcut-label">小红书</div>
                </div>
                
                <!-- 抖音 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #000000;">
                        <i class="fas fa-music"></i>
                    </div>
                    <div class="shortcut-label">抖音</div>
                </div>
                
                <!-- 网易云音乐 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #DD001B;">
                        <i class="fas fa-headphones"></i>
                    </div>
                    <div class="shortcut-label">网易云音乐</div>
                </div>
                
                <!-- 高德地图 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #00B686;">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="shortcut-label">高德地图</div>
                </div>
                
                <!-- 添加 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #F2F2F7;">
                        <i class="fas fa-plus text-gray-400"></i>
                    </div>
                    <div class="shortcut-label">添加</div>
                </div>
            </div>
            
            <!-- 没有快捷访问项的情况 -->
            <div class="shortcuts-empty hidden">
                <div class="text-center py-16">
                    <div class="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-star text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-gray-500 text-lg">暂无快捷访问项</p>
                    <button class="mt-6 px-6 py-3 bg-blue-500 text-white rounded-lg text-base">添加网站</button>
                </div>
            </div>
        </div>
        
        <!-- 最近访问 -->
        <div class="px-6 py-4">
            <div class="section-title flex justify-between items-center mb-5">
                <h2 class="text-2xl font-semibold">最近访问</h2>
                <button class="text-blue-500 text-base">查看全部</button>
            </div>
            
            <div class="recently-visited">
                <!-- 最近访问项 -->
                <div class="recent-item">
                    <div class="recent-favicon" style="background-color: #3388FF;">
                        <span class="text-white">百</span>
                    </div>
                    <div class="recent-info">
                        <div class="recent-title">百度一下，你就知道</div>
                        <div class="recent-url">baidu.com</div>
                    </div>
                </div>
                
                <div class="recent-item">
                    <div class="recent-favicon" style="background-color: #FF4400;">
                        <span class="text-white">淘</span>
                    </div>
                    <div class="recent-info">
                        <div class="recent-title">淘宝网 - 淘！我喜欢</div>
                        <div class="recent-url">taobao.com</div>
                    </div>
                </div>
                
                <div class="recent-item">
                    <div class="recent-favicon" style="background-color: #FB7299;">
                        <i class="fas fa-play text-white"></i>
                    </div>
                    <div class="recent-info">
                        <div class="recent-title">哔哩哔哩 (゜-゜)つロ 干杯~</div>
                        <div class="recent-url">bilibili.com</div>
                    </div>
                </div>
                
                <div class="recent-item">
                    <div class="recent-favicon" style="background-color: #0066FF;">
                        <span class="text-white">知</span>
                    </div>
                    <div class="recent-info">
                        <div class="recent-title">知乎 - 有问题，就会有答案</div>
                        <div class="recent-url">zhihu.com</div>
                    </div>
                </div>
                
                <div class="recent-item">
                    <div class="recent-favicon" style="background-color: #09BB07;">
                        <i class="fas fa-book text-white"></i>
                    </div>
                    <div class="recent-info">
                        <div class="recent-title">微信读书</div>
                        <div class="recent-url">weread.qq.com</div>
                    </div>
                </div>
                
                <div class="recent-item">
                    <div class="recent-favicon" style="background-color: #000000;">
                        <i class="fas fa-music text-white"></i>
                    </div>
                    <div class="recent-info">
                        <div class="recent-title">抖音 - 记录美好生活</div>
                        <div class="recent-url">douyin.com</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新的浏览器工具栏 -->
    <div class="browser-toolbar">
        <div class="browser-toolbar-inner">
            <div class="browser-button">
                <div class="browser-icon">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </div>
                <div class="browser-label">返回</div>
            </div>
            <div class="browser-button">
                <div class="browser-icon">
                    <i class="fas fa-arrow-right text-gray-600"></i>
                </div>
                <div class="browser-label">前进</div>
            </div>
            <div class="browser-button">
                <div class="browser-icon active">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="browser-label">新标签页</div>
            </div>
            <div class="browser-button">
                <div class="browser-icon">
                    <i class="fas fa-layer-group text-gray-600"></i>
                </div>
                <div class="browser-label">标签页</div>
            </div>
            <div class="browser-button">
                <div class="browser-icon">
                    <i class="fas fa-ellipsis-h text-gray-600"></i>
                </div>
                <div class="browser-label">功能</div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
    <script>
        // 示例：切换快捷访问的显示状态（多项/无项）
        function toggleShortcutsDisplay() {
            const shortcutsContainer = document.getElementById('shortcuts-container');
            const shortcutsEmpty = document.querySelector('.shortcuts-empty');
            
            // 根据实际情况显示或隐藏
            // 这里只是演示，实际应用中应根据数据状态决定
            const hasShortcuts = shortcutsContainer.children.length > 1;
            
            if (hasShortcuts) {
                shortcutsContainer.classList.remove('hidden');
                shortcutsEmpty.classList.add('hidden');
            } else {
                shortcutsContainer.classList.add('hidden');
                shortcutsEmpty.classList.remove('hidden');
            }
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化快捷访问显示
            toggleShortcutsDisplay();
        });
    </script>
</body>
</html> 