<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 搜索设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
            transition: background-color 0.2s ease;
        }
        .settings-item:hover {
            background-color: rgba(0, 0, 0, 0.01);
        }
        .settings-item:active {
            background-color: rgba(0, 0, 0, 0.02);
        }
        .settings-item:last-child {
            border-bottom: none;
        }
        
        /* 开关样式优化 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e4e4e4;
            transition: .4s;
            border-radius: 34px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        input:checked + .toggle-slider {
            background-color: #007aff;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        /* 导航栏优化 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        /* 内容区域优化 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        /* 分组标题优化 */
        .group-title {
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #8e8e93;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 4px;
        }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back" onclick="window.history.back()">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">搜索设置</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container">
            <!-- 搜索设置组 -->
            <div class="px-4 py-2">
                <h2 class="group-title">搜索体验</h2>
                <div class="settings-group">
                    <!-- 显示搜索历史 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div>显示搜索历史</div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 显示搜索建议 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div>显示搜索建议</div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 显示"粘贴您复制的内容" -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div>显示"粘贴您复制的内容"</div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索引擎设置组 -->
            <div class="px-4 py-2">
                <h2 class="group-title">搜索引擎</h2>
                <div class="settings-group">
                    <!-- 显示搜索引擎切换条 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div>显示搜索引擎切换条</div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 搜索引擎列表 -->
                    <div class="settings-item" onclick="window.location.href='search_engines.html'">
                        <div class="flex items-center">
                            <div>搜索引擎列表</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 