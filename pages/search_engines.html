<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 搜索引擎列表</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 拖拽提示样式 */
        .drag-hint {
            padding: 12px 16px;
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
        }
        .drag-hint i {
            margin-right: 8px;
            color: #007aff;
        }
        
        /* 拖拽手柄样式 */
        .drag-handle {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            margin-right: 8px;
        }
        
        /* 选中状态样式 */
        .settings-item.selected {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .settings-item.selected .checkmark {
            color: #007aff;
        }
        
        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
            transition: background-color 0.2s ease;
            cursor: pointer;
        }
        .settings-item:hover {
            background-color: rgba(0, 0, 0, 0.01);
        }
        .settings-item:active {
            background-color: rgba(0, 0, 0, 0.02);
        }
        .settings-item:last-child {
            border-bottom: none;
        }
        
        /* 导航栏优化 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        /* 内容区域优化 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        /* 分组标题优化 */
        .group-title {
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #8e8e93;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 4px;
        }

        /* 搜索引擎图标样式 */
        .search-engine-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            border-radius: 4px;
            overflow: hidden;
        }
        .search-engine-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back" onclick="window.history.back()">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">搜索引擎列表</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container">
            <!-- 拖拽提示 -->
            <div class="drag-hint">
                <i class="fas fa-grip-vertical"></i>
                <span>长按并拖动可调整顺序</span>
            </div>
            
            <!-- 搜索引擎列表 -->
            <div class="px-4 py-2">
                <div class="settings-group">
                    <div class="settings-item selected">
                        <div class="flex items-center">
                            <div class="search-engine-icon">
                                <img src="https://www.google.com/favicon.ico" alt="Google">
                            </div>
                            <div>Google</div>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check checkmark"></i>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="search-engine-icon">
                                <img src="https://www.bing.com/favicon.ico" alt="Bing">
                            </div>
                            <div>Bing</div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="search-engine-icon">
                                <img src="https://www.baidu.com/favicon.ico" alt="Baidu">
                            </div>
                            <div>百度</div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="search-engine-icon">
                                <img src="https://www.yahoo.com/favicon.ico" alt="Yahoo">
                            </div>
                            <div>Yahoo</div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="search-engine-icon">
                                <img src="https://duckduckgo.com/favicon.ico" alt="DuckDuckGo">
                            </div>
                            <div>DuckDuckGo</div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="search-engine-icon">
                                <img src="https://www.yandex.com/favicon.ico" alt="Yandex">
                            </div>
                            <div>Yandex</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 添加搜索引擎 -->
            <div class="px-4 py-2">
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #007AFF;">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div>添加搜索引擎</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 