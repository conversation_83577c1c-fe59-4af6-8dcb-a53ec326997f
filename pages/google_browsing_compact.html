<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览Google (简约设计)</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* 自定义样式 */
        .search-bar {
            border-radius: 24px;
            border: 1px solid #dfe1e5;
            box-shadow: 0 1px 6px rgba(32, 33, 36, 0.16);
            transition: all 0.3s ease;
            font-size: 12px;
            height: 32px;
        }
        
        .search-bar:focus-within {
            box-shadow: 0 1px 12px rgba(32, 33, 36, 0.28);
            border-color: transparent;
        }
        
        .google-results {
            font-family: Arial, sans-serif;
        }
        
        .search-result {
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .search-result:hover {
            background-color: #f8f9fa;
        }
        
        .action-icon {
            width: 28px;
            height: 28px;
            min-width: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .action-icon:hover {
            background-color: #f1f3f4;
        }
        
        .script-entry {
            background-color: #4285f4;
            color: white;
        }
        
        .script-entry:hover {
            background-color: #3367d6;
        }
        
        /* 下载进度条提示视图样式 */
        .download-progress-container {
            z-index: 50;
        }
        
        .download-progress, .download-failed, .download-success {
            transition: all 0.3s ease;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
            height: 56px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .download-progress::before, .download-failed::before, .download-success::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            transition: width 0.5s ease;
            z-index: 0;
        }
        
        .download-progress::before {
            width: 28%;
            background-color: rgba(59, 130, 246, 0.08);
        }
        
        .download-failed::before {
            width: 45%;
            background-color: rgba(239, 68, 68, 0.08);
        }
        
        .download-success::before {
            width: 100%;
            background-color: rgba(16, 185, 129, 0.08);
        }
        
        .download-left, .download-middle, .download-right {
            position: relative;
            z-index: 1;
        }
        
        .download-middle {
            padding: 0 4px;
        }
        
        .filename {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .download-info {
            opacity: 0.8;
        }
        
        .download-btn, .retry-btn, .success-icon, .open-file-btn, .cancel-btn {
            transition: all 0.2s ease;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .download-btn:hover, .retry-btn:hover, .success-icon:hover, .open-file-btn:hover, .cancel-btn:hover {
            transform: scale(1.05);
        }
        
        .cancel-btn {
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .cancel-btn:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        /* 调整内容区域的底部内边距，为下载进度条腾出空间 */
        .content-area {
            padding-bottom: 200px;
        }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 浏览器顶部搜索栏 - 简约单行设计 -->
    <div class="browser-top-bar bg-white px-1 pt-14 pb-2 sticky top-0 z-10 border-b border-gray-200">
        <div class="flex items-center gap-1">
            <!-- 左侧导航/主菜单按钮 -->
            <div class="action-icon" title="菜单">
                <i class="fas fa-bars text-gray-600"></i>
            </div>
            
            <!-- 搜索框 -->
            <div class="search-bar flex items-center flex-1 px-2 py-1 relative">
                <img src="https://www.google.com/favicon.ico" class="w-4 h-4 mr-1" alt="Google">
                <div class="flex-1 truncate text-xs font-medium">modern web</div>
            </div>
            
            <!-- 功能图标组 -->
            <div class="flex items-center">
                <!-- 脚本入口 -->
                <div class="action-icon script-entry mr-1" title="脚本">
                    <i class="fas fa-code"></i>
                </div>
                
                <!-- 刷新按钮 -->
                <div class="action-icon mr-1" title="刷新">
                    <i class="fas fa-redo-alt text-gray-600"></i>
                </div>
                
                <!-- 更多按钮 -->
                <div class="action-icon" title="更多">
                    <i class="fas fa-ellipsis-v text-gray-600"></i>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Google 搜索结果内容区域 -->
    <div class="content-area google-results pt-2 pb-20 h-full overflow-y-auto">
        <!-- Google Logo -->
        <div class="flex justify-center mt-2 mb-6">
            <img src="https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png" alt="Google" class="h-7">
        </div>
        
        <!-- 搜索信息栏 -->
        <div class="text-sm text-gray-600 px-4 pb-2 border-b border-gray-200">
            约 1,760,000,000 条结果 (0.48 秒)
        </div>
        
        <!-- 搜索结果 -->
        <div class="search-results px-4 py-3">
            <!-- 结果 1 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.example.com › modern-web-design</div>
                <div class="text-blue-700 text-lg font-medium mb-1">2023现代网页设计趋势与最佳实践 - Example</div>
                <div class="text-sm text-gray-800">现代网页设计强调简洁性、响应式布局和用户体验。了解最新的设计趋势，包括深色模式、微交互和沉浸式3D效果...</div>
            </div>
            
            <!-- 结果 2 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.webdesigntrends.com</div>
                <div class="text-blue-700 text-lg font-medium mb-1">网页设计的10大现代趋势 | Web Design Trends</div>
                <div class="text-sm text-gray-800">发现2023年主导网页设计的关键趋势。从新拟态UI到声音设计，了解当今最流行的设计方向...</div>
            </div>
            
            <!-- 结果 3 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.designacademy.edu/blog</div>
                <div class="text-blue-700 text-lg font-medium mb-1">如何掌握现代网页设计的基础 - Design Academy</div>
                <div class="text-sm text-gray-800">现代网页设计结合了美学和功能性。本指南介绍了核心原则，包括排版、色彩理论、空间利用和视觉层次结构...</div>
            </div>
            
            <!-- 结果 4 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.creativestudio.com/resources</div>
                <div class="text-blue-700 text-lg font-medium mb-1">30个令人惊艳的现代网页设计案例 - Creative Studio</div>
                <div class="text-sm text-gray-800">从全球顶尖品牌和创意工作室中精选的30个杰出网站设计。每个案例都包含详细分析和关键设计要素解释...</div>
            </div>
            
            <!-- 结果 5 -->
            <div class="search-result py-3 border-b border-gray-100">
                <div class="text-xs text-gray-600 mb-1">https://www.techinsider.io/web-design</div>
                <div class="text-blue-700 text-lg font-medium mb-1">现代网页设计必备的5款设计工具 - Tech Insider</div>
                <div class="text-sm text-gray-800">探索专业设计师推荐的顶级网页设计工具。从Figma到Adobe XD，这些工具将帮助你创建优雅且用户友好的网站...</div>
            </div>
            
            <!-- 相关搜索 -->
            <div class="related-searches mt-8">
                <div class="text-lg text-gray-800 mb-3">相关搜索</div>
                <div class="grid grid-cols-2 gap-2">
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">现代网页设计课程</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">网页设计最新技术</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">UI/UX 设计趋势</div>
                    <div class="bg-gray-100 p-3 rounded-lg text-sm">前端开发框架比较</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 下载进度条提示视图 -->
    <div class="download-progress-container fixed bottom-12 left-0 w-full px-3">
        <!-- 下载中状态 -->
        <div class="download-progress bg-white border border-gray-200 rounded-lg flex items-center px-4 justify-between">
            <div class="download-left">
                <button class="download-btn bg-blue-500 text-white flex items-center justify-center">
                    <i class="fas fa-arrow-down text-sm"></i>
                </button>
            </div>
            <div class="download-middle flex-1 mx-3">
                <div class="filename text-sm truncate">现代网页设计指南.pdf</div>
                <div class="download-info text-xs text-gray-500">2.4MB/8.7MB</div>
            </div>
            <div class="download-right">
                <button class="cancel-btn text-gray-500 flex items-center justify-center">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        </div>
        
        <!-- 下载失败状态 -->
        <div class="download-failed bg-white border border-gray-200 rounded-lg flex items-center px-4 justify-between">
            <div class="download-left">
                <button class="retry-btn bg-red-500 text-white flex items-center justify-center">
                    <i class="fas fa-redo-alt text-sm"></i>
                </button>
            </div>
            <div class="download-middle flex-1 mx-3">
                <div class="filename text-sm truncate">设计资源合集.zip</div>
                <div class="download-info text-xs text-red-500">下载失败，请重试</div>
            </div>
            <div class="download-right">
                <button class="cancel-btn text-gray-500 flex items-center justify-center">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        </div>
        
        <!-- 下载成功状态 -->
        <div class="download-success bg-white border border-gray-200 rounded-lg flex items-center px-4 justify-between">
            <div class="download-left">
                <button class="success-icon bg-green-500 text-white flex items-center justify-center">
                    <i class="fas fa-check text-sm"></i>
                </button>
            </div>
            <div class="download-middle flex-1 mx-3">
                <div class="filename text-sm truncate">UI设计模板.sketch</div>
                <div class="download-info text-xs text-gray-500">12.5MB</div>
            </div>
            <div class="download-right flex items-center">
                <button class="open-file-btn bg-blue-500 text-white flex items-center justify-center mr-2">
                    <i class="fas fa-folder-open text-sm"></i>
                </button>
                <button class="cancel-btn text-gray-500 flex items-center justify-center">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <div class="tab-icon"><i class="fas fa-home"></i></div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-layer-group"></i></div>
            <div class="tab-label">标签页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-bookmark"></i></div>
            <div class="tab-label">书签</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-history"></i></div>
            <div class="tab-label">历史</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-cog"></i></div>
            <div class="tab-label">设置</div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html>