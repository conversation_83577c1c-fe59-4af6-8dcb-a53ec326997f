<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 下载管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }

        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }

        .content-area {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            background-color: #f8f9fa;
        }

        /* 分类标签样式 */
        .category-tabs {
            display: flex;
            margin-bottom: 16px;
            background: white;
            border-radius: 12px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .category-tab {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .category-tab.active {
            background: #f0f7ff;
            color: #2196f3;
        }

        /* 下载项样式 */
        .download-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .download-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #666;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .file-size {
            font-size: 12px;
            color: #666;
        }

        .download-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            margin-left: 8px;
        }

        .status-downloading {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-completed {
            background: #e8f5e9;
            color: #2e7d32;
        }

        .status-failed {
            background: #ffebee;
            color: #c62828;
        }

        /* 下载中状态特有样式 */
        .progress-container {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .progress-bar {
            flex: 1;
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #2196f3;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 12px;
            color: #666;
            min-width: 40px;
            text-align: right;
        }

        /* 下载完成状态特有样式 */
        .completed-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .completed-info i {
            margin-right: 4px;
            color: #2e7d32;
        }

        /* 下载失败状态特有样式 */
        .failed-info {
            font-size: 12px;
            color: #c62828;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .failed-info i {
            margin-right: 4px;
        }

        .download-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .action-button {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .action-button.primary {
            background: #2196f3;
            color: white;
        }

        .action-button.secondary {
            background: #f5f5f5;
            color: #333;
        }

        .action-button.danger {
            background: #ffebee;
            color: #c62828;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 16px;
        }

        /* 分类标题 */
        .category-title {
            font-size: 16px;
            font-weight: 600;
            margin: 16px 0 8px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="nav-bar">
        <a href="settings.html" class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </a>
        <div class="nav-title">下载管理</div>
    </div>

    <div class="content-area">
        <!-- 分类标签 -->
        <div class="category-tabs">
            <div class="category-tab active">全部</div>
            <div class="category-tab">下载中</div>
            <div class="category-tab">已完成</div>
            <div class="category-tab">已失败</div>
        </div>

        <!-- 下载项列表 -->
        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">项目方案.pdf</div>
                    <div class="file-size">2.5 MB</div>
                </div>
                <div class="download-status status-downloading">下载中</div>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 65%"></div>
                </div>
                <div class="progress-text">65%</div>
            </div>
            <div class="download-actions">
                <button class="action-button secondary">暂停</button>
                <button class="action-button danger">取消</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file-word"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">会议记录.docx</div>
                    <div class="file-size">1.8 MB</div>
                </div>
                <div class="download-status status-completed">已完成</div>
            </div>
            <div class="download-actions">
                <button class="action-button primary">打开</button>
                <button class="action-button secondary">删除</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">说明文档.txt</div>
                    <div class="file-size">0.8 MB</div>
                </div>
                <div class="download-status status-downloading">下载中</div>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 35%"></div>
                </div>
                <div class="progress-text">35%</div>
            </div>
            <div class="download-actions">
                <button class="action-button secondary">暂停</button>
                <button class="action-button danger">取消</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file-excel"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">财务报表.xlsx</div>
                    <div class="file-size">3.2 MB</div>
                </div>
                <div class="download-status status-failed">下载失败</div>
            </div>
            <div class="download-actions">
                <button class="action-button primary">重试</button>
                <button class="action-button danger">删除</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">安装程序.exe</div>
                    <div class="file-size">45.2 MB</div>
                </div>
                <div class="download-status status-downloading">下载中</div>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 82%"></div>
                </div>
                <div class="progress-text">82%</div>
            </div>
            <div class="download-actions">
                <button class="action-button secondary">暂停</button>
                <button class="action-button danger">取消</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file-powerpoint"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">产品介绍.pptx</div>
                    <div class="file-size">15.6 MB</div>
                </div>
                <div class="download-status status-completed">已完成</div>
            </div>
            <div class="download-actions">
                <button class="action-button primary">打开</button>
                <button class="action-button secondary">删除</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">手机应用.apk</div>
                    <div class="file-size">32.7 MB</div>
                </div>
                <div class="download-status status-completed">已完成</div>
            </div>
            <div class="download-actions">
                <button class="action-button primary">打开</button>
                <button class="action-button secondary">删除</button>
            </div>
        </div>

        <div class="download-item">
            <div class="download-header">
                <div class="file-icon">
                    <i class="fas fa-file"></i>
                </div>
                <div class="file-info">
                    <div class="file-name">未知文件.xyz</div>
                    <div class="file-size">3.2 MB</div>
                </div>
                <div class="download-status status-failed">下载失败</div>
            </div>
            <div class="download-actions">
                <button class="action-button primary">重试</button>
                <button class="action-button danger">删除</button>
            </div>
        </div>
    </div>
</body>
</html> 