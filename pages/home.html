<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .search-bar {
            background-color: rgba(142, 142, 147, 0.12);
            border-radius: 10px;
        }
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }
        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .shortcut-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 24px;
            color: white;
        }
        .shortcut-label {
            font-size: 12px;
            font-weight: 500;
            text-align: center;
        }
        .shortcuts-empty {
            padding: 30px 0;
            text-align: center;
        }
        .browser-toolbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 10px 16px;
            display: flex;
            justify-content: space-between;
            z-index: 100;
        }
        .browser-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
        }
        .browser-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 4px;
        }
        .browser-label {
            font-size: 10px;
            color: #6b7280;
            white-space: nowrap;
        }
        .browser-icon.active {
            background-color: #3b82f6;
            color: white;
        }
        @media (min-width: 640px) {
            .shortcuts-grid {
                grid-template-columns: repeat(5, 1fr);
            }
        }
        @media (min-width: 768px) {
            .shortcuts-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }
        /* 会员入口样式 */
        .member-banner {
            background: linear-gradient(to right, #FFEDBC, #FFD66F);
            border-radius: 10px;
            padding: 8px 15px;
            margin: 0 16px 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .member-banner .icon {
            background-color: #FFBD17;
            color: white;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 12px;
        }
        .member-banner .content {
            flex: 1;
            display: flex;
            align-items: center;
        }
        .member-banner .title {
            font-size: 13px;
            font-weight: 600;
            color: #975500;
            white-space: nowrap;
        }
        .member-banner .subtitle {
            font-size: 11px;
            color: #975500;
            opacity: 0.8;
            margin-left: 6px;
            white-space: nowrap;
        }
        .member-banner .action {
            background-color: #FF9500;
            color: white;
            border-radius: 12px;
            padding: 3px 8px;
            font-size: 11px;
            font-weight: 500;
            white-space: nowrap;
            margin-left: 8px;
        }
        /* 关闭按钮样式 */
        .member-banner .close-btn {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            opacity: 0.5;
            font-size: 10px;
            color: #975500;
            border-radius: 50%;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        .member-banner .close-btn:hover {
            opacity: 0.8;
            background-color: rgba(0, 0, 0, 0.05);
        }
        /* 弹窗动画样式 */
        .ai-dialog {
            opacity: 0;
            transform: scale(0.95);
            transition: all 0.2s ease-out;
        }
        
        .ai-dialog.scale-100 {
            opacity: 1;
            transform: scale(1);
        }
        
        /* 输入框获取焦点时的脉冲效果 */
        #ai-question-input:focus {
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
        }
        
        /* 提交按钮悬停效果 */
        #ai-dialog-submit:hover i {
            transform: translateX(2px);
            transition: transform 0.2s ease;
        }
        
        /* 禁用按钮样式 */
        #ai-dialog-submit:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area pt-14 pb-32 h-full overflow-y-auto relative">
        <!-- 搜索栏 -->
        <div class="px-4 py-4">
            <div class="search-bar flex items-center px-3 py-3">
                <i class="fas fa-search text-gray-500 mr-2"></i>
                <input type="text" placeholder="搜索或输入网址" class="bg-transparent w-full outline-none text-base">
                <i class="fas fa-microphone text-gray-500 ml-2"></i>
            </div>
        </div>
        
        <!-- 会员入口横幅 -->
        <div class="member-banner">
            <div class="flex items-center">
                <div class="icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="content">
                    <div class="title">开通会员</div>
                    <div class="subtitle">无广告浏览</div>
                </div>
            </div>
            <div class="flex items-center">
                <div class="action">
                    立即开通
                </div>
                <div class="close-btn">
                    <i class="fas fa-times"></i>
                </div>
            </div>
        </div>
        
        <!-- 快捷访问 -->
        <div class="px-4 py-2">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">快捷访问</h2>
                <button class="text-blue-500 text-sm">编辑</button>
            </div>
            
            <!-- 有快捷访问项的情况 -->
            <div class="shortcuts-grid" id="shortcuts-container">
                <!-- 百度 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #3388FF;">
                        <span class="text-white font-bold">百</span>
                    </div>
                    <div class="shortcut-label">百度</div>
                </div>
                
                <!-- 微博 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #E6162D;">
                        <i class="fab fa-weibo"></i>
                    </div>
                    <div class="shortcut-label">微博</div>
                </div>
                
                <!-- 淘宝 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #FF4400;">
                        <span class="text-white font-bold">淘</span>
                    </div>
                    <div class="shortcut-label">淘宝</div>
                </div>
                
                <!-- 京东 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #E1251B;">
                        <span class="text-white font-bold">京</span>
                    </div>
                    <div class="shortcut-label">京东</div>
                </div>
                
                <!-- 知乎 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #0066FF;">
                        <span class="text-white font-bold">知</span>
                    </div>
                    <div class="shortcut-label">知乎</div>
                </div>
                
                <!-- 哔哩哔哩 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #FB7299;">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="shortcut-label">哔哩哔哩</div>
                </div>
                
                <!-- 微信读书 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #09BB07;">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="shortcut-label">微信读书</div>
                </div>
                
                <!-- 秘塔AI -->
                <div class="shortcut-item" id="mita-ai-shortcut">
                    <div class="shortcut-icon" style="background-color: #6C5CE7;">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="shortcut-label">秘塔AI</div>
                </div>
                
                <!-- 添加 -->
                <div class="shortcut-item">
                    <div class="shortcut-icon" style="background-color: #F2F2F7;">
                        <i class="fas fa-plus text-gray-400"></i>
                    </div>
                    <div class="shortcut-label">添加</div>
                </div>
            </div>
            
            <!-- 没有快捷访问项的情况 -->
            <div class="shortcuts-empty hidden">
                <div class="text-center py-10">
                    <div class="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-star text-gray-400 text-xl"></i>
                    </div>
                    <p class="text-gray-500">暂无快捷访问项</p>
                    <button class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg text-sm">添加网站</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增AI输入弹窗 -->
    <div id="ai-dialog-overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="ai-dialog bg-white rounded-xl w-5/6 max-w-md overflow-hidden shadow-xl transform transition-all">
            <div class="px-6 py-4 bg-gradient-to-r from-purple-600 to-indigo-600">
                <h3 class="text-lg font-medium text-white flex items-center">
                    <i class="fas fa-robot mr-2"></i>
                    <span>秘塔AI助手</span>
                </h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-gray-600 mb-4">请输入您想要咨询的问题:</p>
                <div class="relative">
                    <textarea id="ai-question-input" class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 min-h-[100px] text-sm" placeholder="例如：帮我写一封申请信..."></textarea>
                    <div class="absolute bottom-3 right-3 text-xs text-gray-400 hidden" id="ai-input-counter">0/200</div>
                </div>
                <div class="flex items-center justify-between space-x-3 mt-6">
                    <button id="ai-dialog-cancel" class="flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200 text-sm font-medium">
                        取消
                    </button>
                    <button id="ai-dialog-submit" class="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-lg transition-colors duration-200 text-sm font-medium flex items-center justify-center">
                        <span>提问</span>
                        <i class="fas fa-paper-plane ml-2 text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新的浏览器工具栏 -->
    <div class="browser-toolbar">
        <div class="browser-button">
            <div class="browser-icon">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </div>
            <div class="browser-label">返回</div>
        </div>
        <div class="browser-button">
            <div class="browser-icon">
                <i class="fas fa-arrow-right text-gray-600"></i>
            </div>
            <div class="browser-label">前进</div>
        </div>
        <div class="browser-button">
            <div class="browser-icon active">
                <i class="fas fa-plus"></i>
            </div>
            <div class="browser-label">新标签页</div>
        </div>
        <div class="browser-button">
            <div class="browser-icon">
                <i class="fas fa-layer-group text-gray-600"></i>
            </div>
            <div class="browser-label">标签页</div>
        </div>
        <div class="browser-button">
            <div class="browser-icon">
                <i class="fas fa-ellipsis-h text-gray-600"></i>
            </div>
            <div class="browser-label">功能</div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
    <script>
        // 示例：切换快捷访问的显示状态（多项/无项）
        function toggleShortcutsDisplay() {
            const shortcutsContainer = document.getElementById('shortcuts-container');
            const shortcutsEmpty = document.querySelector('.shortcuts-empty');
            
            // 根据实际情况显示或隐藏
            // 这里只是演示，实际应用中应根据数据状态决定
            const hasShortcuts = shortcutsContainer.children.length > 1;
            
            if (hasShortcuts) {
                shortcutsContainer.classList.remove('hidden');
                shortcutsEmpty.classList.add('hidden');
            } else {
                shortcutsContainer.classList.add('hidden');
                shortcutsEmpty.classList.remove('hidden');
            }
        }
        
        // 会员横幅相关功能
        function setupMemberBanner() {
            const memberBanner = document.querySelector('.member-banner');
            const actionButton = memberBanner.querySelector('.action');
            const closeButton = memberBanner.querySelector('.close-btn');
            
            // 点击整个横幅或按钮都会跳转到会员页
            memberBanner.addEventListener('click', function(e) {
                // 如果点击的是关闭按钮，不执行跳转
                if (e.target.closest('.close-btn')) {
                    return;
                }
                // 这里替换为实际的会员页面URL
                window.location.href = '../pages/member.html';
            });
            
            // 点击关闭按钮
            closeButton.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡，避免触发横幅的点击事件
                
                // 记住用户关闭了横幅，设置一个过期时间（例如7天后）
                const expiryTime = Date.now() + (7 * 24 * 60 * 60 * 1000); // 7天后的时间戳
                localStorage.setItem('memberBannerDismissed', expiryTime);
                
                // 隐藏横幅
                memberBanner.classList.add('hidden');
            });
            
            // 按照用户会员状态和关闭状态显示或隐藏横幅
            function checkBannerStatus() {
                // 检查用户是否为会员
                const isMember = localStorage.getItem('isMember') === 'true';
                
                // 检查用户是否关闭了横幅且关闭时间未过期
                const dismissedTime = localStorage.getItem('memberBannerDismissed');
                const isDismissed = dismissedTime && Number(dismissedTime) > Date.now();
                
                // 如果是会员或用户已关闭横幅，则隐藏横幅
                if (isMember || isDismissed) {
                    memberBanner.classList.add('hidden');
                } else {
                    memberBanner.classList.remove('hidden');
                }
            }
            
            // 初始检查横幅状态
            checkBannerStatus();
            
            // 为测试目的添加的功能：双击横幅可以切换会员状态（仅用于演示）
            memberBanner.addEventListener('dblclick', function(e) {
                e.stopPropagation(); // 阻止事件冒泡，避免触发单击事件
                const currentStatus = localStorage.getItem('isMember') === 'true';
                localStorage.setItem('isMember', !currentStatus);
                
                // 清除关闭状态，确保会员状态的变化能够立即显示
                localStorage.removeItem('memberBannerDismissed');
                
                checkBannerStatus();
            });
        }
        
        // 秘塔AI弹窗相关功能
        function setupAIDialog() {
            const shortcut = document.getElementById('mita-ai-shortcut');
            const overlay = document.getElementById('ai-dialog-overlay');
            const dialog = overlay.querySelector('.ai-dialog');
            const cancelBtn = document.getElementById('ai-dialog-cancel');
            const submitBtn = document.getElementById('ai-dialog-submit');
            const questionInput = document.getElementById('ai-question-input');
            const inputCounter = document.getElementById('ai-input-counter');
            
            // 点击快捷方式，显示弹窗
            shortcut.addEventListener('click', function() {
                overlay.classList.remove('hidden');
                // 添加动画效果
                setTimeout(() => {
                    dialog.classList.add('scale-100', 'opacity-100');
                    questionInput.focus();
                }, 10);
            });
            
            // 显示输入字数计数
            questionInput.addEventListener('input', function() {
                const length = this.value.length;
                inputCounter.textContent = `${length}/200`;
                inputCounter.classList.remove('hidden');
                
                // 如果超过字数限制，显示警告
                if (length > 200) {
                    inputCounter.classList.add('text-red-500');
                    submitBtn.disabled = true;
                } else {
                    inputCounter.classList.remove('text-red-500');
                    submitBtn.disabled = false;
                }
            });
            
            // 点击取消按钮，关闭弹窗
            cancelBtn.addEventListener('click', closeDialog);
            
            // 点击弹窗外部，关闭弹窗
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closeDialog();
                }
            });
            
            // 提交问题
            submitBtn.addEventListener('click', function() {
                const question = questionInput.value.trim();
                if (question) {
                    // 添加提交效果
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    submitBtn.disabled = true;
                    
                    // 模拟请求延迟
                    setTimeout(() => {
                        // 这里可以替换为实际的跳转逻辑
                        window.location.href = `../pages/ai-result.html?q=${encodeURIComponent(question)}`;
                    }, 800);
                }
            });
            
            // 监听回车键提交
            questionInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    submitBtn.click();
                }
            });
            
            // 关闭弹窗函数
            function closeDialog() {
                dialog.classList.remove('scale-100', 'opacity-100');
                setTimeout(() => {
                    overlay.classList.add('hidden');
                    questionInput.value = '';
                    inputCounter.classList.add('hidden');
                }, 200);
            }
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化快捷访问显示
            toggleShortcutsDisplay();
            
            // 初始化会员横幅
            setupMemberBanner();
            
            // 初始化秘塔AI弹窗
            setupAIDialog();
        });
    </script>
</body>
</html> 