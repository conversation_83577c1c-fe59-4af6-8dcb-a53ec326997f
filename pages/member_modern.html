<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 会员介绍</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background-color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            color: #333;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .nav-bar {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background-color: #fff;
            z-index: 100;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-back {
            position: absolute;
            left: 16px;
            color: #007aff;
        }
        
        .nav-restore {
            position: absolute;
            right: 16px;
            color: #007aff;
            font-size: 14px;
        }
        
        .header-section {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header-desc {
            font-size: 16px;
            color: #666;
            margin-bottom: 24px;
            max-width: 280px;
        }
        
        .price-tag {
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 30px;
            box-shadow: 0 4px 10px rgba(55, 81, 255, 0.2);
        }
        
        .feature-section {
            padding: 0 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 8px;
            color: #3751FF;
        }
        
        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 30px;
        }
        
        .feature-item {
            background: #f8f9fd;
            border-radius: 16px;
            padding: 16px;
            display: flex;
            align-items: flex-start;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-item:after {
            content: "";
            position: absolute;
            right: -20px;
            bottom: -20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(55, 81, 255, 0.1), rgba(55, 81, 255, 0));
        }
        
        .feature-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
            flex-shrink: 0;
            color: white;
            font-size: 18px;
        }
        
        .feature-content {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 4px;
            color: #333;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .bottom-cta {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: white;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }
        
        .btn-purchase {
            background: linear-gradient(135deg, #3751FF, #6E8AFF);
            color: white;
            border-radius: 14px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            text-align: center;
            box-shadow: 0 4px 10px rgba(55, 81, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: transform 0.2s ease;
            z-index: 1;
        }
        
        .btn-purchase:active {
            transform: scale(0.98);
        }
        
        .btn-purchase::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
            z-index: -1;
        }
        
        .btn-purchase:hover::after {
            left: 100%;
        }
        
        .highlight-tag {
            position: absolute;
            top: -8px;
            right: 10px;
            background: linear-gradient(135deg, #FF6B6B, #FF9F43);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 3px 10px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(255, 107, 107, 0.3);
            z-index: 1;
        }
        
        .benefits-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 80px;
        }
        
        .benefit-item {
            background: #f0f4ff;
            border-radius: 12px;
            padding: 10px;
            display: flex;
            align-items: center;
        }
        
        .benefit-icon {
            width: 30px;
            height: 30px;
            border-radius: 8px;
            background-color: rgba(55, 81, 255, 0.1);
            color: #3751FF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .benefit-text {
            font-size: 13px;
            font-weight: 500;
            color: #333;
        }
        
        .content-wrapper {
            padding-bottom: 100px;
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">会员介绍</div>
        <div class="nav-restore">恢复购买</div>
    </div>
    
    <div class="content-wrapper">
        <!-- 头部信息 -->
        <!-- <div class="header-section">
            <div class="header-title">Pro 高级会员</div>
            <div class="header-desc">一次购买，永久使用，解锁全部高级功能</div>
            <div class="price-tag">永久激活 · ¥38.00</div>
        </div> -->
        
        <!-- 功能列表 -->
        <div class="feature-section">
            <div class="section-title">
                <i class="fas fa-crown"></i>专属功能
            </div>
            <div class="feature-list">
                <!-- 功能1：无广告体验 -->
                <div class="feature-item">
                    <div class="highlight-tag">热门</div>
                    <div class="feature-icon" style="background-color: #FF3B30;">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">永久无广告</div>
                        <div class="feature-desc">专属会员体验，浏览网页再无广告打扰</div>
                    </div>
                </div>
                
                <!-- 功能2：手动标记 -->
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #FF9500;">
                        <i class="fas fa-highlighter"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">手动标记</div>
                        <div class="feature-desc">轻松标记网页元素，一键去除不想看到的内容</div>
                    </div>
                </div>
                
                <!-- 功能3：双引擎翻译 -->
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #5AC8FA;">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">双引擎翻译</div>
                        <div class="feature-desc">网页一键翻译，支持多种翻译引擎和语言</div>
                    </div>
                </div>
                
                <!-- 功能4：油猴增强 -->
                <div class="feature-item">
                    <div class="highlight-tag">高级</div>
                    <div class="feature-icon" style="background-color: #5856D6;">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">油猴增强</div>
                        <div class="feature-desc">支持脚本更新与黑白名单等高级功能</div>
                    </div>
                </div>
                
                <!-- 功能5：自动翻页 -->
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #AF52DE;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">自动翻页</div>
                        <div class="feature-desc">网页自动翻页和智能拼页等高级特性</div>
                    </div>
                </div>
                
                <!-- 功能6：iCloud同步 -->
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #64D2FF;">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">iCloud同步</div>
                        <div class="feature-desc">支持书签、脚本和标记等数据备份</div>
                    </div>
                </div>
                
                <!-- 功能7：多平台支持 -->
                <div class="feature-item">
                    <div class="feature-icon" style="background-color: #34C759;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-content">
                        <div class="feature-title">多平台支持</div>
                        <div class="feature-desc">支持iPhone/iPad多设备同时使用</div>
                    </div>
                </div>
            </div>
            
            <!-- 会员权益 -->
            <div class="section-title">
                <i class="fas fa-gift"></i>会员权益
            </div>
            <div class="benefits-list">
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <div class="benefit-text">永久使用</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="benefit-text">持续更新</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="benefit-text">数据保护</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="benefit-text">优先客服</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部购买按钮 -->
    <div class="bottom-cta">
        <button class="btn-purchase">立即购买 · ¥38.00</button>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加按钮点击效果
            const purchaseBtn = document.querySelector('.btn-purchase');
            purchaseBtn.addEventListener('click', function() {
                this.style.transform = 'scale(0.97)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
            
            // 添加功能项点击效果
            const featureItems = document.querySelectorAll('.feature-item');
            featureItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        });
    </script>
</body>
</html> 