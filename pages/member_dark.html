<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 会员介绍</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background-color: #111827;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            color: #e5e7eb;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .nav-bar {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background-color: #1f2937;
            z-index: 100;
        }
        
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #f9fafb;
        }
        
        .nav-back {
            position: absolute;
            left: 16px;
            color: #60a5fa;
        }
        
        .nav-restore {
            position: absolute;
            right: 16px;
            color: #60a5fa;
            font-size: 14px;
        }
        
        .header-section {
            padding: 24px;
            text-align: center;
            position: relative;
        }
        
        .glow-effect {
            position: absolute;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(79, 70, 229, 0.2) 0%, rgba(79, 70, 229, 0) 70%);
            top: 0;
            right: 0;
            z-index: 0;
        }
        
        .glow-effect-2 {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(219, 39, 119, 0.15) 0%, rgba(219, 39, 119, 0) 70%);
            bottom: 20px;
            left: 20px;
            z-index: 0;
        }
        
        .header-icon {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #4f46e5, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: white;
            position: relative;
            z-index: 1;
            box-shadow: 0 0 20px rgba(79, 70, 229, 0.4);
        }
        
        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(to right, #4f46e5, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        
        .header-desc {
            font-size: 16px;
            color: #9ca3af;
            margin-bottom: 24px;
            position: relative;
            z-index: 1;
        }
        
        .price-tag {
            background: linear-gradient(135deg, #4f46e5, #8b5cf6);
            color: white;
            border-radius: 16px;
            padding: 10px 24px;
            font-size: 20px;
            font-weight: 700;
            margin: 0 auto 40px;
            display: inline-block;
            position: relative;
            z-index: 1;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }
        
        .feature-section {
            padding: 0 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #f9fafb;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            color: #8b5cf6;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background-color: #1f2937;
            border-radius: 16px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .feature-card:active {
            transform: translateY(2px);
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card::after {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(var(--feature-color-rgb), 0.15) 0%, rgba(var(--feature-color-rgb), 0) 70%);
            bottom: -40px;
            right: -40px;
        }
        
        .card-top {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 14px;
        }
        
        .feature-title {
            font-weight: 600;
            font-size: 17px;
            color: #f9fafb;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #9ca3af;
            line-height: 1.5;
        }
        
        .highlight-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #f43f5e, #ec4899);
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 20px;
            z-index: 1;
        }
        
        .benefits-container {
            background-color: #1f2937;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 100px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .benefit-item:last-child {
            margin-bottom: 0;
        }
        
        .benefit-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background-color: rgba(139, 92, 246, 0.2);
            color: #a78bfa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
        }
        
        .benefit-text {
            font-size: 15px;
            color: #d1d5db;
        }
        
        .bottom-cta {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background-color: rgba(17, 24, 39, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 100;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .btn-purchase {
            background: linear-gradient(135deg, #4f46e5, #8b5cf6);
            color: white;
            border-radius: 14px;
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            text-align: center;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn-purchase::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
            z-index: -1;
        }
        
        .btn-purchase:active::before {
            left: 100%;
        }
        
        .particles-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
        
        .particle {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            opacity: 0.2;
            animation: float 15s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100vh) rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 粒子背景 -->
    <div class="particles-container" id="particles"></div>
    
    <!-- iOS 状态栏 (暗色) -->
    <div class="ios-status-bar" style="background-color: #111827; color: white;">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">会员介绍</div>
        <div class="nav-restore">恢复购买</div>
    </div>
    
    <!-- 头部信息 -->
    <!-- <div class="header-section">
        <div class="glow-effect"></div>
        <div class="glow-effect-2"></div>
        
        <div class="header-icon">
            <i class="fas fa-crown"></i>
        </div>
        <div class="header-title">Pro 高级会员</div>
        <div class="header-desc">解锁全部高级功能，畅享极致体验</div>
        <div class="price-tag">永久激活 · ¥38.00</div>
    </div> -->
    
    <!-- 会员功能 -->
    <div class="feature-section">
        <div class="section-title">
            <i class="fas fa-star"></i>专属功能
        </div>
        <div class="features-grid">
            <!-- 功能1：无广告体验 -->
            <div class="feature-card" style="--feature-color-rgb: 248, 113, 113">
                <div class="highlight-badge">热门</div>
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #ef4444;">
                        <i class="fas fa-ban"></i>
                    </div>
                    <div class="feature-title">永久无广告</div>
                </div>
                <div class="feature-desc">专属会员体验，浏览网页再无广告打扰</div>
            </div>
            
            <!-- 功能2：手动标记 -->
            <div class="feature-card" style="--feature-color-rgb: 251, 146, 60">
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #f97316;">
                        <i class="fas fa-highlighter"></i>
                    </div>
                    <div class="feature-title">手动标记</div>
                </div>
                <div class="feature-desc">轻松标记网页元素，一键去除不想看到的内容</div>
            </div>
            
            <!-- 功能3：双引擎翻译 -->
            <div class="feature-card" style="--feature-color-rgb: 96, 165, 250">
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #3b82f6;">
                        <i class="fas fa-language"></i>
                    </div>
                    <div class="feature-title">双引擎翻译</div>
                </div>
                <div class="feature-desc">网页一键翻译，支持多种翻译引擎和语言</div>
            </div>
            
            <!-- 功能4：油猴增强 -->
            <div class="feature-card" style="--feature-color-rgb: 139, 92, 246">
                <div class="highlight-badge">高级</div>
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #8b5cf6;">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="feature-title">油猴增强</div>
                </div>
                <div class="feature-desc">支持脚本更新与黑白名单等高级功能</div>
            </div>
            
            <!-- 功能5：自动翻页 -->
            <div class="feature-card" style="--feature-color-rgb: 192, 132, 252">
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #a855f7;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="feature-title">自动翻页</div>
                </div>
                <div class="feature-desc">网页自动翻页和智能拼页等高级特性</div>
            </div>
            
            <!-- 功能6：iCloud同步 -->
            <div class="feature-card" style="--feature-color-rgb: 125, 211, 252">
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #0ea5e9;">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <div class="feature-title">iCloud同步</div>
                </div>
                <div class="feature-desc">支持书签、脚本和标记等数据备份</div>
            </div>
            
            <!-- 功能7：多平台支持 -->
            <div class="feature-card" style="--feature-color-rgb: 110, 231, 183">
                <div class="card-top">
                    <div class="feature-icon" style="background-color: #10b981;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-title">多平台支持</div>
                </div>
                <div class="feature-desc">支持iPhone/iPad多设备同时使用</div>
            </div>
        </div>
        
        <!-- 会员权益 -->
        <div class="section-title">
            <i class="fas fa-gift"></i>会员特权
        </div>
        <div class="benefits-container">
            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-infinity"></i>
                </div>
                <div class="benefit-text">一次付费，永久使用</div>
            </div>
            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <div class="benefit-text">持续更新，获得最新功能</div>
            </div>
            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="benefit-text">隐私保护，数据安全</div>
            </div>
            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="benefit-text">专属客服，优先响应</div>
            </div>
        </div>
    </div>
    
    <!-- 底部购买按钮 -->
    <div class="bottom-cta">
        <button class="btn-purchase">立即购买 · ¥38.00</button>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 创建背景粒子
            createParticles();
            
            // 按钮点击效果
            const purchaseBtn = document.querySelector('.btn-purchase');
            purchaseBtn.addEventListener('click', function() {
                this.style.transform = 'scale(0.97)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            });
            
            // 功能卡片点击效果
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'translateY(2px)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(0)';
                    }, 100);
                });
            });
        });
        
        // 创建背景粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particlesCount = 20;
            
            for (let i = 0; i < particlesCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');
                
                // 随机大小 (1-3px)
                const size = Math.random() * 2 + 1;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                
                // 随机位置
                const posX = Math.random() * 100;
                const posY = Math.random() * 100 + 100; // 从底部开始
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                
                // 随机速度
                const duration = Math.random() * 10 + 10;
                particle.style.animationDuration = `${duration}s`;
                
                // 随机延迟
                const delay = Math.random() * 5;
                particle.style.animationDelay = `${delay}s`;
                
                // 随机透明度
                const opacity = Math.random() * 0.2 + 0.1;
                particle.style.opacity = opacity;
                
                // 添加到容器
                particlesContainer.appendChild(particle);
            }
        }
    </script>
</body>
</html> 