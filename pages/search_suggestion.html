<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>搜索联想</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        :root {
            --bg-color: #f5f5f5;
            --item-bg-color: #ffffff;
            --text-color: #333333;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --shadow-opacity: 0.1;
            --border-color: #f0f0f0;
        }

        .dark-mode {
            --bg-color: #121212;
            --item-bg-color: #1e1e1e;
            --text-color: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --shadow-opacity: 0.3;
            --border-color: #4a4a4a;
        }

        body {
            background-color: var(--bg-color);
            margin: 0;
            padding: 0;
            transition: background-color 0.3s ease;
        }

        .search-engine-list {
            height: 44px;
            display: flex;
            overflow-x: auto;
            padding: 8px 16px;
            gap: 8px;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            background-color: var(--bg-color);
            transition: background-color 0.3s ease;
        }

        .search-engine-list::-webkit-scrollbar {
            display: none;
        }

        .search-engine-item {
            display: flex;
            align-items: center;
            padding: 6px 12px;
            background-color: var(--item-bg-color);
            border-radius: 20px;
            box-shadow: 0 2px 4px var(--shadow-color);
            white-space: nowrap;
            flex-shrink: 0;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .search-engine-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .dark-mode .search-engine-item {
            background: #1e1e1e;
            border: 1px solid var(--border-color);
            box-shadow: none;
        }

        .dark-mode .search-engine-item:hover {
            background: #2a2a2a;
            border: 1px solid var(--border-color);
            box-shadow: none;
        }

        .search-engine-icon {
            margin-right: 6px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .search-engine-item:hover .search-engine-icon {
            transform: scale(1.1);
        }

        .search-engine-name {
            font-size: 14px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .dark-mode .search-engine-name {
            color: #ffffff;
            text-shadow: none;
        }

        /* 主题切换按钮 */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            background-color: var(--item-bg-color);
            color: var(--text-color);
            box-shadow: 0 2px 4px var(--shadow-color);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            z-index: 1000;
        }

        .dark-mode .theme-toggle {
            background: #1e1e1e;
            border: 1px solid var(--border-color);
            color: #ffffff;
            box-shadow: none;
        }

        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .dark-mode .theme-toggle:hover {
            background: #2a2a2a;
            border: 1px solid var(--border-color);
            box-shadow: none;
        }

        /* 搜索联想页特定样式 */
        .search-container {
            padding: 16px;
            background-color: #f5f5f5;
            border-radius: 8px;
            margin: 16px;
            display: flex;
            align-items: center;
        }
        
        .search-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 16px;
            outline: none;
            color: #333;
        }
        
        .search-input::placeholder {
            color: #999;
        }
        
        /* 搜索引擎标题 */
        .search-engine-title {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            font-size: 14px;
            color: #666;
        }
        
        .search-engine-title i {
            margin-right: 8px;
        }
        
        /* 水平滑动容器 */
        .horizontal-scroll-container {
            position: relative;
            padding: 0 16px 16px;
            display: flex;
            align-items: center;
        }
        
        .horizontal-scroll-container::-webkit-scrollbar {
            display: none;
        }
        
        .search-engine-icon {
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .suggestions-scroll {
            flex: 1;
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
            display: flex;
        }
        
        .suggestions-scroll::-webkit-scrollbar {
            display: none;
        }
        
        /* 水平滑动项 */
        .horizontal-item {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 8px;
            background-color: #f9f9f9;
            border-radius: 16px;
            text-align: center;
            vertical-align: top;
        }
        
        .horizontal-item:last-child {
            margin-right: 0;
        }
        
        .horizontal-item-title {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 列表项 */
        .suggestion-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .suggestion-icon {
            width: 25px;
            height: 25px;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #222;
        }
        
        .suggestion-content {
            flex: 1;
        }
        
        .suggestion-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .suggestion-arrow {
            width: 20px;
            height: 20px;
            color: #222;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* 暗黑模式 */
        .dark-mode .search-container {
            background-color: #222;
        }
        
        .dark-mode .search-input {
            color: #fff;
        }
        
        .dark-mode .search-input::placeholder {
            color: #999;
        }
        
        .dark-mode .search-engine-title {
            color: #999;
        }
        
        .dark-mode .horizontal-item {
            background-color: #1a1a1a;
        }
        
        .dark-mode .horizontal-item-title {
            color: #fff;
        }
        
        .dark-mode .suggestion-item {
            border-bottom: 1px solid #333;
        }
        
        .dark-mode .suggestion-icon,
        .dark-mode .suggestion-arrow {
            color: #fff;
        }
        
        .dark-mode .suggestion-title {
            color: #fff;
        }
        
        /* iPad适配 */
        @media (min-width: 768px) {
            .search-container {
                margin: 20px;
                padding: 20px;
            }
            
            .search-icon {
                width: 25px;
                height: 25px;
            }
            
            .search-input {
                font-size: 18px;
            }
            
            .search-engine-title {
                padding: 15px 20px;
                font-size: 16px;
            }
            
            .horizontal-scroll-container {
                padding: 0 20px 20px;
            }
            
            .horizontal-item {
                padding: 10px 20px;
                margin-right: 12px;
            }
            
            .horizontal-item-title {
                font-size: 16px;
            }
            
            .suggestion-item {
                padding: 20px;
            }
            
            .suggestion-icon {
                width: 30px;
                height: 30px;
            }
            
            .suggestion-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon"></i> 切换主题
    </button>

    <!-- iOS状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">9:41</span>
        </div>
        <div class="status-right">
            <i class="fas fa-signal signal-icon"></i>
            <i class="fas fa-wifi wifi-icon"></i>
            <i class="fas fa-battery-full battery-icon"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="flex justify-between items-center px-4 py-3 bg-white border-b border-gray-200" style="margin-top: 44px;">
        <div class="flex items-center flex-1">
            <div class="search-container">
                <div class="search-icon">
                    <i class="fab fa-google" style="color: #4285F4;"></i>
                </div>
                <input type="text" class="search-input" placeholder="搜索或输入网址" id="searchInput">
            </div>
        </div>
        <button class="text-blue-500 font-medium ml-2">取消</button>
    </div>
    
    <!-- 搜索联想列表 -->
    <div class="suggestions-list">
        <!-- 百度搜索联想项 - 水平滑动 -->
        <div class="horizontal-scroll-container" id="baiduSuggestions">
            <div class="search-engine-icon">
                <i class="fab fa-baidu" style="color: #4285F4;"></i>
            </div>
            <div class="suggestions-scroll">
                <div class="horizontal-item">
                    <div class="horizontal-item-title">百度搜索建议 1</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">百度搜索建议 2</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">百度搜索建议 3</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">百度搜索建议 4</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">百度搜索建议 5</div>
                </div>
            </div>
        </div>
        
        <!-- 秘塔搜索联想项 - 水平滑动 -->
        <div class="horizontal-scroll-container" id="metasoSuggestions">
            <div class="search-engine-icon">
                <i class="fas fa-robot" style="color: #FF6B6B;"></i>
            </div>
            <div class="suggestions-scroll">
                <div class="horizontal-item">
                    <div class="horizontal-item-title">秘塔搜索建议 1</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">秘塔搜索建议 2</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">秘塔搜索建议 3</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">秘塔搜索建议 4</div>
                </div>
                <div class="horizontal-item">
                    <div class="horizontal-item-title">秘塔搜索建议 5</div>
                </div>
            </div>
        </div>
        
        <!-- 默认搜索引擎联想项 - 列表形式 -->
        <div class="search-engine-title">
            <i class="fas fa-search" style="color: #4285F4;"></i>
            <span>默认搜索</span>
        </div>
        <div id="defaultSuggestions">
            <div class="suggestion-item">
                <div class="suggestion-icon">
                    <i class="fas fa-search" style="color: #4285F4;"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-title">默认搜索建议 1</div>
                </div>
                <div class="suggestion-arrow">
                    <i class="fas fa-arrow-up-left"></i>
                </div>
            </div>
            
            <div class="suggestion-item">
                <div class="suggestion-icon">
                    <i class="fas fa-search" style="color: #4285F4;"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-title">默认搜索建议 2</div>
                </div>
                <div class="suggestion-arrow">
                    <i class="fas fa-arrow-up-left"></i>
                </div>
            </div>
            
            <div class="suggestion-item">
                <div class="suggestion-icon">
                    <i class="fas fa-search" style="color: #4285F4;"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-title">默认搜索建议 3</div>
                </div>
                <div class="suggestion-arrow">
                    <i class="fas fa-arrow-up-left"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="search-engine-list">
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fab fa-google text-red-500"></i>
            </div>
            <span class="search-engine-name">Google</span>
        </div>
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fab fa-baidu text-blue-500"></i>
            </div>
            <span class="search-engine-name">百度</span>
        </div>
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fas fa-robot text-purple-500"></i>
            </div>
            <span class="search-engine-name">秘塔AI</span>
        </div>
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fab fa-microsoft text-blue-600"></i>
            </div>
            <span class="search-engine-name">必应</span>
        </div>
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fas fa-search text-green-500"></i>
            </div>
            <span class="search-engine-name">DuckDuckGo</span>
        </div>
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fas fa-yin-yang text-gray-700"></i>
            </div>
            <span class="search-engine-name">Yandex</span>
        </div>
        <div class="search-engine-item">
            <div class="search-engine-icon">
                <i class="fas fa-dog text-orange-500"></i>
            </div>
            <span class="search-engine-name">Dogpile</span>
        </div>
    </div>
    
    <script>
        // 搜索输入处理
        const searchInput = document.getElementById('searchInput');
        
        // 模拟搜索联想功能
        searchInput.addEventListener('input', function(e) {
            const searchText = e.target.value;
            if (searchText.length > 0) {
                // 这里可以添加实际的搜索联想逻辑
                console.log('搜索文本:', searchText);
                
                // 模拟更新搜索建议内容
                updateSuggestions(searchText);
            }
        });
        
        // 模拟更新搜索建议内容
        function updateSuggestions(searchText) {
            // 更新百度搜索建议
            const baiduItems = document.querySelectorAll('#baiduSuggestions .horizontal-item-title');
            baiduItems.forEach((item, index) => {
                item.textContent = searchText ? `${searchText} 相关建议 ${index + 1}` : `百度搜索建议 ${index + 1}`;
            });
            
            // 更新秘塔搜索建议
            const metasoItems = document.querySelectorAll('#metasoSuggestions .horizontal-item-title');
            metasoItems.forEach((item, index) => {
                item.textContent = searchText ? `${searchText} 相关建议 ${index + 1}` : `秘塔搜索建议 ${index + 1}`;
            });
            
            // 更新默认搜索建议
            const defaultItems = document.querySelectorAll('#defaultSuggestions .suggestion-title');
            defaultItems.forEach((item, index) => {
                item.textContent = searchText ? `${searchText} 相关建议 ${index + 1}` : `默认搜索建议 ${index + 1}`;
            });
        }
        
        // 模拟暗黑模式切换
        function toggleTheme() {
            document.body.classList.toggle('dark-mode');
            const themeToggle = document.querySelector('.theme-toggle');
            const icon = themeToggle.querySelector('i');
            
            if (document.body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i> 切换主题';
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i> 切换主题';
            }
        }
    </script>
</body>
</html> 