<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 阅读模式背景主题</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 选中状态样式 */
        .settings-item.selected {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .settings-item.selected .checkmark {
            color: #007aff;
        }

        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
            transition: background-color 0.2s ease;
            cursor: pointer;
        }
        .settings-item:hover {
            background-color: rgba(0, 0, 0, 0.01);
        }
        .settings-item:active {
            background-color: rgba(0, 0, 0, 0.02);
        }
        .settings-item:last-child {
            border-bottom: none;
        }

        /* 导航栏优化 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }

        /* 内容区域优化 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }

        /* 分组标题优化 */
        .group-title {
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #8e8e93;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 4px;
        }

        /* 主题颜色预览样式 */
        .theme-color-preview {
            width: 28px;
            height: 28px;
            margin-right: 12px;
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* 背景颜色定义 */
        .bg-white-preview { background-color: #ffffff; }
        .bg-beige-preview { background-color: #f5f5dc; }
        .bg-dark-preview { background-color: #2d3748; }
        .bg-green-preview { background-color: #c6f6d5; }
        .bg-blue-preview { background-color: #e6f3ff; }
        .bg-pink-preview { background-color: #fce7f3; }
        .bg-yellow-preview { background-color: #fef3c7; }
        .bg-gray-preview { background-color: #f3f4f6; }
        .bg-purple-preview { background-color: #ede9fe; }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back" onclick="window.history.back()">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">阅读模式背景主题</div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container">
            <!-- 主题列表 -->
            <div class="px-4 py-2">
                <div class="settings-group">
                    <div class="settings-item selected" data-theme="white">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-white-preview"></div>
                            <div>白色</div>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check checkmark"></i>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="beige">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-beige-preview"></div>
                            <div>米色</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="dark">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-dark-preview"></div>
                            <div>深色</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="green">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-green-preview"></div>
                            <div>护眼绿</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="blue">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-blue-preview"></div>
                            <div>护眼蓝</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="pink">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-pink-preview"></div>
                            <div>粉色</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="yellow">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-yellow-preview"></div>
                            <div>暖黄</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="gray">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-gray-preview"></div>
                            <div>灰色</div>
                        </div>
                    </div>

                    <div class="settings-item" data-theme="purple">
                        <div class="flex items-center">
                            <div class="theme-color-preview bg-purple-preview"></div>
                            <div>紫色</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const themeItems = document.querySelectorAll('.settings-item[data-theme]');

            // 加载保存的主题设置
            function loadSavedTheme() {
                const savedTheme = localStorage.getItem('readingTheme') || 'white';
                selectTheme(savedTheme);
            }

            // 选择主题
            function selectTheme(theme) {
                // 移除所有选中状态
                themeItems.forEach(item => {
                    item.classList.remove('selected');
                    const checkmark = item.querySelector('.checkmark');
                    if (checkmark) {
                        checkmark.remove();
                    }
                });

                // 添加选中状态到指定主题
                const selectedItem = document.querySelector(`[data-theme="${theme}"]`);
                if (selectedItem) {
                    selectedItem.classList.add('selected');
                    const rightDiv = selectedItem.querySelector('.flex:last-child');
                    if (rightDiv && !rightDiv.querySelector('.checkmark')) {
                        rightDiv.innerHTML = '<i class="fas fa-check checkmark"></i>';
                    }
                }
            }

            // 主题选择事件监听
            themeItems.forEach(item => {
                item.addEventListener('click', function() {
                    const theme = this.dataset.theme;
                    selectTheme(theme);

                    // 保存到本地存储
                    localStorage.setItem('readingTheme', theme);

                    // 可选：添加触觉反馈效果
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                });
            });

            // 初始化
            loadSavedTheme();
        });
    </script>
</body>
</html>
