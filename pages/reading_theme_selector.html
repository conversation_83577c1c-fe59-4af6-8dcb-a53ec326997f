<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>阅读模式背景主题</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* 页面背景样式 */
        .theme-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            transition: all 0.3s ease;
        }

        /* 主容器样式 */
        .theme-container {
            background-color: white;
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            margin: 0 auto;
            position: relative;
        }

        /* 标题样式 */
        .theme-title {
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .theme-subtitle {
            text-align: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
        }

        /* 主题选项网格 */
        .theme-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }

        /* 主题选项样式 */
        .theme-option {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        .theme-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16px 8px;
            border: 2px solid #e8e8e8;
            border-radius: 12px;
            background-color: #ffffff;
            transition: all 0.3s ease;
            gap: 8px;
            min-height: 80px;
        }

        .theme-option:hover .theme-card {
            border-color: #4285f4;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
        }

        .theme-option input[type="radio"]:checked + .theme-card {
            border-color: #4285f4;
            background-color: #f8f9ff;
            box-shadow: 0 4px 16px rgba(66, 133, 244, 0.2);
        }

        /* 颜色预览样式 */
        .color-preview {
            width: 32px;
            height: 20px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 主题名称样式 */
        .theme-name {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            text-align: center;
            transition: color 0.3s ease;
        }

        .theme-option input[type="radio"]:checked + .theme-card .theme-name {
            color: #4285f4;
        }

        /* 选中指示器 */
        .check-indicator {
            position: absolute;
            top: 6px;
            right: 6px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: #4285f4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.3s ease;
        }

        .theme-option input[type="radio"]:checked + .theme-card .check-indicator {
            opacity: 1;
            transform: scale(1);
        }

        /* 预览区域 */
        .preview-area {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .preview-text {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            text-align: center;
        }

        /* 底部按钮 */
        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            outline: none;
        }

        .btn-primary {
            background-color: #4285f4;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3367d6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        }

        .btn-secondary {
            background-color: #f1f3f4;
            color: #5f6368;
        }

        .btn-secondary:hover {
            background-color: #e8eaed;
        }

        /* 背景颜色定义 */
        .bg-white-preview { background-color: #ffffff; }
        .bg-beige-preview { background-color: #f5f5dc; }
        .bg-dark-preview { background-color: #2d3748; }
        .bg-green-preview { background-color: #c6f6d5; }
        .bg-blue-preview { background-color: #e6f3ff; }
        .bg-pink-preview { background-color: #fce7f3; }
        .bg-yellow-preview { background-color: #fef3c7; }
        .bg-gray-preview { background-color: #f3f4f6; }
        .bg-purple-preview { background-color: #ede9fe; }

        /* 主题背景样式 */
        .theme-white { background-color: #ffffff !important; color: #333 !important; }
        .theme-beige { background-color: #f5f5dc !important; color: #333 !important; }
        .theme-dark { background-color: #2d3748 !important; color: #e2e8f0 !important; }
        .theme-green { background-color: #c6f6d5 !important; color: #2d3748 !important; }
        .theme-blue { background-color: #e6f3ff !important; color: #1e40af !important; }
        .theme-pink { background-color: #fce7f3 !important; color: #be185d !important; }
        .theme-yellow { background-color: #fef3c7 !important; color: #92400e !important; }
        .theme-gray { background-color: #f3f4f6 !important; color: #374151 !important; }
        .theme-purple { background-color: #ede9fe !important; color: #6b21a8 !important; }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .theme-container {
                margin: 10px;
                padding: 20px;
            }
            
            .theme-grid {
                gap: 8px;
            }
            
            .theme-card {
                padding: 12px 6px;
                min-height: 70px;
            }
            
            .color-preview {
                width: 28px;
                height: 18px;
            }
        }
    </style>
</head>
<body class="theme-page">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="theme-container">
        <!-- 标题区域 -->
        <div class="header-section">
            <h1 class="theme-title">阅读模式背景主题</h1>
            <p class="theme-subtitle">选择您喜欢的阅读背景颜色</p>
        </div>

        <!-- 预览区域 -->
        <div class="preview-area" id="previewArea">
            <p class="preview-text">
                这是阅读模式的预览文本。选择不同的背景主题可以减少眼部疲劳，提供更舒适的阅读体验。
            </p>
        </div>

        <!-- 主题选择网格 -->
        <div class="theme-grid">
            <label class="theme-option">
                <input type="radio" name="theme" value="white" checked>
                <div class="theme-card">
                    <div class="color-preview bg-white-preview"></div>
                    <span class="theme-name">白色</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="beige">
                <div class="theme-card">
                    <div class="color-preview bg-beige-preview"></div>
                    <span class="theme-name">米色</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="dark">
                <div class="theme-card">
                    <div class="color-preview bg-dark-preview"></div>
                    <span class="theme-name">深色</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="green">
                <div class="theme-card">
                    <div class="color-preview bg-green-preview"></div>
                    <span class="theme-name">护眼绿</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="blue">
                <div class="theme-card">
                    <div class="color-preview bg-blue-preview"></div>
                    <span class="theme-name">护眼蓝</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="pink">
                <div class="theme-card">
                    <div class="color-preview bg-pink-preview"></div>
                    <span class="theme-name">粉色</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="yellow">
                <div class="theme-card">
                    <div class="color-preview bg-yellow-preview"></div>
                    <span class="theme-name">暖黄</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="gray">
                <div class="theme-card">
                    <div class="color-preview bg-gray-preview"></div>
                    <span class="theme-name">灰色</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>

            <label class="theme-option">
                <input type="radio" name="theme" value="purple">
                <div class="theme-card">
                    <div class="color-preview bg-purple-preview"></div>
                    <span class="theme-name">紫色</span>
                    <div class="check-indicator">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
            </label>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-secondary" id="resetBtn">
                <i class="fas fa-undo mr-2"></i>重置
            </button>
            <button class="btn btn-primary" id="saveBtn">
                <i class="fas fa-save mr-2"></i>保存设置
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const themeInputs = document.querySelectorAll('input[name="theme"]');
            const previewArea = document.getElementById('previewArea');
            const saveBtn = document.getElementById('saveBtn');
            const resetBtn = document.getElementById('resetBtn');

            // 加载保存的主题设置
            function loadSavedTheme() {
                const savedTheme = localStorage.getItem('readingTheme') || 'white';
                const themeInput = document.querySelector(`input[value="${savedTheme}"]`);
                if (themeInput) {
                    themeInput.checked = true;
                    applyTheme(savedTheme);
                }
            }

            // 应用主题到预览区域
            function applyTheme(theme) {
                // 移除所有主题类
                previewArea.classList.remove('theme-white', 'theme-beige', 'theme-dark', 'theme-green', 'theme-blue', 'theme-pink', 'theme-yellow', 'theme-gray', 'theme-purple');
                // 添加新主题类
                previewArea.classList.add(`theme-${theme}`);
            }

            // 主题选择事件监听
            themeInputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (this.checked) {
                        applyTheme(this.value);
                    }
                });
            });

            // 保存按钮事件
            saveBtn.addEventListener('click', function() {
                const selectedTheme = document.querySelector('input[name="theme"]:checked').value;
                localStorage.setItem('readingTheme', selectedTheme);
                
                // 显示保存成功提示
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-2"></i>已保存';
                this.style.backgroundColor = '#34a853';
                
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.backgroundColor = '#4285f4';
                }, 1500);
            });

            // 重置按钮事件
            resetBtn.addEventListener('click', function() {
                const whiteTheme = document.querySelector('input[value="white"]');
                whiteTheme.checked = true;
                applyTheme('white');
                localStorage.removeItem('readingTheme');
                
                // 显示重置成功提示
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check mr-2"></i>已重置';
                this.style.backgroundColor = '#34a853';
                
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.backgroundColor = '#f1f3f4';
                }, 1500);
            });

            // 初始化
            loadSavedTheme();
        });
    </script>
</body>
</html>
