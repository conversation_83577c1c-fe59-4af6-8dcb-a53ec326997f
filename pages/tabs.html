<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 标签页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .tab-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        .tab-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        .tab-preview {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        .tab-info {
            padding: 8px;
            background-color: white;
        }
        .tab-close {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .new-tab-card {
            border: 2px dashed #e5e7eb;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 160px;
            border-radius: 12px;
        }
        .toolbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 10px 16px;
            display: flex;
            justify-content: space-between;
            z-index: 100;
        }
        .toolbar-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
        }
        .toolbar-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 4px;
        }
        .toolbar-label {
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area pt-14 pb-32 h-full overflow-y-auto relative">
        <!-- 导航栏 -->
        <div class="nav-bar px-4 py-2 flex items-center justify-between">
            <button class="w-8 h-8 flex items-center justify-center text-gray-500">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="text-lg font-bold">标签页</div>
            <button class="w-8 h-8 flex items-center justify-center text-gray-500">
                <i class="fas fa-cog"></i>
            </button>
        </div>
        
        <!-- 搜索栏 -->
        <div class="px-4 py-3">
            <div class="bg-gray-100 rounded-lg px-3 py-2 flex items-center">
                <i class="fas fa-search text-gray-500 mr-2"></i>
                <input type="text" placeholder="搜索标签页" class="bg-transparent w-full outline-none text-sm">
            </div>
        </div>
        
        <!-- 标签页计数 -->
        <div class="px-4 py-2">
            <div class="text-sm text-gray-500">5个标签页</div>
        </div>
        
        <!-- 标签页网格视图 -->
        <div class="px-4 py-2">
            <div class="tab-grid">
                <!-- 标签页1 - 百度 -->
                <div class="tab-card">
                    <img src="https://images.unsplash.com/photo-1481487196290-c152efe083f5?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2VhcmNoJTIwZW5naW5lfGVufDB8fDB8fHww" class="tab-preview" alt="百度">
                    <div class="tab-info">
                        <div class="text-xs font-medium truncate">百度一下，你就知道</div>
                        <div class="text-xs text-gray-500 truncate">www.baidu.com</div>
                    </div>
                    <button class="tab-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 标签页2 - 知乎 -->
                <div class="tab-card">
                    <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fHF1ZXN0aW9ufGVufDB8fDB8fHww" class="tab-preview" alt="知乎">
                    <div class="tab-info">
                        <div class="text-xs font-medium truncate">知乎 - 有问题，就会有答案</div>
                        <div class="text-xs text-gray-500 truncate">www.zhihu.com</div>
                    </div>
                    <button class="tab-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 标签页3 - 微博 -->
                <div class="tab-card">
                    <img src="https://images.unsplash.com/photo-1611162618071-b39a2ec055fb?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fHNvY2lhbCUyMG1lZGlhfGVufDB8fDB8fHww" class="tab-preview" alt="微博">
                    <div class="tab-info">
                        <div class="text-xs font-medium truncate">微博 - 随时随地发现新鲜事</div>
                        <div class="text-xs text-gray-500 truncate">www.weibo.com</div>
                    </div>
                    <button class="tab-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 标签页4 - 淘宝 -->
                <div class="tab-card">
                    <img src="https://images.unsplash.com/photo-1472851294608-062f824d29cc?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8c2hvcHBpbmd8ZW58MHx8MHx8fDA%3D" class="tab-preview" alt="淘宝">
                    <div class="tab-info">
                        <div class="text-xs font-medium truncate">淘宝网 - 淘！我喜欢</div>
                        <div class="text-xs text-gray-500 truncate">www.taobao.com</div>
                    </div>
                    <button class="tab-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 标签页5 - 哔哩哔哩 -->
                <div class="tab-card">
                    <img src="https://images.unsplash.com/photo-1611162616475-46b635cb6868?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dmlkZW8lMjBwbGF0Zm9ybXxlbnwwfHwwfHx8MA%3D%3D" class="tab-preview" alt="哔哩哔哩">
                    <div class="tab-info">
                        <div class="text-xs font-medium truncate">哔哩哔哩 - 国内知名视频弹幕网站</div>
                        <div class="text-xs text-gray-500 truncate">www.bilibili.com</div>
                    </div>
                    <button class="tab-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 新建标签页 -->
                <div class="new-tab-card">
                    <div class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white mb-2">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="text-sm font-medium text-gray-700">新建标签页</div>
                </div>
            </div>
        </div>
        
        <!-- 隐私浏览入口 -->
        <div class="px-4 py-4">
            <button class="w-full py-3 bg-gray-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-mask text-gray-700 mr-2"></i>
                <span class="text-gray-700 font-medium">开启隐私浏览模式</span>
            </button>
        </div>
    </div>
    
    <!-- 新的底部工具栏 -->
    <div class="toolbar">
        <div class="toolbar-button">
            <div class="toolbar-icon">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </div>
            <div class="toolbar-label">返回</div>
        </div>
        <div class="toolbar-button">
            <div class="toolbar-icon bg-blue-500">
                <i class="fas fa-plus text-white"></i>
            </div>
            <div class="toolbar-label">新建</div>
        </div>
        <div class="toolbar-button">
            <div class="toolbar-icon bg-blue-500">
                <i class="fas fa-times text-white"></i>
            </div>
            <div class="toolbar-label">关闭全部</div>
        </div>
        <div class="toolbar-button">
            <div class="toolbar-icon">
                <i class="fas fa-cog text-gray-600"></i>
            </div>
            <div class="toolbar-label">设置</div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 