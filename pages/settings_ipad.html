<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - iPad设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            max-width: 1024px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
        }
        .top-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            background-color: #fff;
            max-width: 1024px;
            margin: 0 auto;
        }
        .settings-container {
            display: flex;
            height: calc(100vh - 100px);
            padding-top: 100px;
        }
        .settings-sidebar {
            width: 280px;
            background-color: #f3f4f6;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            flex-shrink: 0;
            height: 100%;
        }
        .settings-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            height: 100%;
            padding-bottom: 50px;
        }
        .sidebar-item {
            padding: 14px 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        .sidebar-item.active {
            background-color: #fff;
            border-left: 3px solid #007aff;
        }
        .sidebar-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            flex-shrink: 0;
        }
        .sidebar-title {
            font-weight: 500;
            color: #1f2937;
        }
        .settings-group {
            border-radius: 10px;
            background-color: #fff;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        .settings-item:last-child {
            border-bottom: none;
        }
        .settings-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: white;
            font-size: 16px;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 54px;
            height: 30px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e4e4e4;
            transition: .4s;
            border-radius: 34px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-color: #007aff;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(22px);
        }
        .pro-badge {
            background-color: #007aff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 16px 20px;
            background-color: #f9fafb;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        .nav-back {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 18px;
        }
        .nav-title {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }
        .settings-description {
            font-size: 14px;
            color: #6b7280;
            margin-top: 8px;
        }
    </style>
</head>
<body class="bg-white">
    <!-- 顶部容器（包含状态栏和导航栏） -->
    <div class="top-container">
        <!-- iOS 状态栏 -->
        <div class="ios-status-bar">
            <div class="status-left">
                <span class="time">14:42</span>
            </div>
            <div class="status-right">
                <span class="signal-icon"><i class="fas fa-signal"></i></span>
                <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
                <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-back">
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="nav-title">设置</div>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="settings-container">
        <!-- 侧边栏 -->
        <div class="settings-sidebar">
            <div class="sidebar-item active">
                <div class="sidebar-icon" style="background-color: #007AFF;">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="sidebar-title">Pro高级版</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon" style="background-color: #5856D6;">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <div class="sidebar-title">基础设置</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon" style="background-color: #AF52DE;">
                    <i class="fas fa-puzzle-piece"></i>
                </div>
                <div class="sidebar-title">进阶功能</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon" style="background-color: #FF2D55;">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="sidebar-title">隐私与安全</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon" style="background-color: #30B0C7;">
                    <i class="fas fa-question-circle"></i>
                </div>
                <div class="sidebar-title">支持与帮助</div>
            </div>
            <div class="sidebar-item">
                <div class="sidebar-icon" style="background-color: #8E8E93;">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="sidebar-title">关于</div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="settings-content">
            <!-- Pro高级版 -->
            <div class="section-title">Pro高级版</div>
            <div class="settings-group bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                <div class="settings-item border-none">
                    <div class="flex items-center">
                        <div class="settings-icon bg-white text-blue-500">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div>
                            <div class="font-bold text-lg">升级到Pro高级版</div>
                            <div class="text-sm opacity-80 mt-1">解锁所有高级功能，提升浏览体验</div>
                        </div>
                    </div>
                    <div>
                        <button class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium">了解详情</button>
                    </div>
                </div>
            </div>
            
            <div class="mt-6">
                <div class="text-gray-500 font-medium mb-3">Pro专享功能</div>
                <div class="settings-group">
                    <!-- 自动翻页 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #AF52DE;">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>
                                <div>自动翻页</div>
                                <div class="settings-description">浏览长文自动翻页，减少手动操作</div>
                            </div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 拦截跳转 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF9500;">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <div>拦截跳转</div>
                                <div class="settings-description">智能识别并阻止恶意网站跳转</div>
                            </div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 无广告阅读 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #34C759;">
                                <i class="fas fa-book-reader"></i>
                            </div>
                            <div>
                                <div>无广告阅读</div>
                                <div class="settings-description">增强版广告拦截，提供更纯净的阅读体验</div>
                            </div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 基础设置 -->
            <div class="mt-8">
                <div class="section-title">基础设置</div>
                <div class="settings-group">
                    <!-- 通用设置 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #5856D6;">
                                <i class="fas fa-sliders-h"></i>
                            </div>
                            <div>通用设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 主页设置 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF9500;">
                                <i class="fas fa-home"></i>
                            </div>
                            <div>主页设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 网页设置 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #34C759;">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div>网页设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 添加更多设置项 -->
            <div class="mt-8">
                <div class="section-title">隐私与安全</div>
                <div class="settings-group">
                    <!-- 广告过滤 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF2D55;">
                                <i class="fas fa-ban"></i>
                            </div>
                            <div>
                                <div>广告过滤</div>
                                <div class="settings-description">拦截网页广告内容</div>
                            </div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 隐私保护 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #5856D6;">
                                <i class="fas fa-user-secret"></i>
                            </div>
                            <div>
                                <div>隐私保护</div>
                                <div class="settings-description">阻止网站跟踪您的浏览行为</div>
                            </div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 清理数据 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF3B30;">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                            <div>
                                <div>清理数据</div>
                                <div class="settings-description">删除浏览历史、缓存和Cookie</div>
                            </div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
    <script>
        // 侧边栏切换
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active类
                    sidebarItems.forEach(i => i.classList.remove('active'));
                    // 添加active类到当前点击项
                    this.classList.add('active');
                    
                    // 这里可以添加内容区域切换逻辑
                    // 例如根据点击的侧边栏项显示不同内容
                });
            });
        });
    </script>
</body>
</html> 