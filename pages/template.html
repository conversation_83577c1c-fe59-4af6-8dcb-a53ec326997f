<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器页面模板</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area pt-14 pb-20 h-full overflow-y-auto relative">
        <!-- 页面内容将在这里 -->
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <div class="tab-icon"><i class="fas fa-home"></i></div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-layer-group"></i></div>
            <div class="tab-label">标签页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-bookmark"></i></div>
            <div class="tab-label">书签</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-history"></i></div>
            <div class="tab-label">历史</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-cog"></i></div>
            <div class="tab-label">设置</div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 