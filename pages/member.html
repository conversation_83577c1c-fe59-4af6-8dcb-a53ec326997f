<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 会员介绍</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            background-color: #f9fafb;
        }
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: #fff;
            z-index: 10;
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
        }
        .nav-restore {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #007aff;
            font-size: 14px;
            font-weight: 500;
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
        }
        .member-container {
            padding: 20px;
            position: relative;
        }
        .member-header {
            background: linear-gradient(135deg, #3751FF 0%, #3481FF 100%);
            border-radius: 16px;
            padding: 24px;
            color: white;
            text-align: center;
            margin-bottom: 24px;
            box-shadow: 0 10px 20px rgba(55, 81, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        .member-header::before {
            content: "";
            position: absolute;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
            top: -150px;
            right: -150px;
            border-radius: 50%;
        }
        .member-header::after {
            content: "";
            position: absolute;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%);
            bottom: -100px;
            left: -100px;
            border-radius: 50%;
        }
        .member-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            position: relative;
        }
        .member-subtitle {
            opacity: 0.9;
            font-size: 15px;
            margin-bottom: 20px;
            position: relative;
        }
        .member-price {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 4px;
            position: relative;
        }
        .member-price-desc {
            opacity: 0.8;
            font-size: 14px;
            position: relative;
            margin-bottom: 20px;
        }
        .feature-slider-container {
            margin: 0 -20px;
            position: relative;
            padding: 0 20px;
        }
        .feature-slider {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
            padding: 10px 0;
            margin: 0 -10px;
            scroll-padding: 0 20px;
        }
        .feature-slider::-webkit-scrollbar {
            display: none;
        }
        .feature-card {
            flex: 0 0 auto;
            width: 260px;
            background-color: white;
            border-radius: 16px;
            padding: 20px;
            margin: 0 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            scroll-snap-align: start;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .feature-card:active {
            transform: scale(0.98);
        }
        .feature-card::after {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(var(--feature-color-rgb), 0.1) 0%, rgba(var(--feature-color-rgb), 0) 70%);
            bottom: -50px;
            right: -50px;
            border-radius: 50%;
        }
        .feature-icon-wrapper {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 16px;
        }
        .feature-title {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 8px;
            color: #1f2937;
        }
        .feature-desc {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3751FF 0%, #3481FF 100%);
            color: white;
            font-weight: 600;
            padding: 16px 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 10px rgba(55, 81, 255, 0.2);
            display: block;
            width: 100%;
            margin-top: 30px;
            position: relative;
            overflow: hidden;
            transition: transform 0.2s ease;
        }
        .btn-primary:active {
            transform: scale(0.98);
        }
        .btn-primary::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }
        .btn-primary:hover::after {
            left: 100%;
        }
        .crown-icon {
            width: 70px;
            height: 70px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 30px;
            position: relative;
            z-index: 1;
        }
        .crown-icon::after {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            z-index: -1;
            animation: pulse 2s infinite;
        }
        .feature-indicator {
            display: flex;
            justify-content: center;
            margin-top: 16px;
            position: relative;
        }
        .indicator-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #d1d5db;
            margin: 0 4px;
        }
        .indicator-dot.active {
            background-color: #3751FF;
            width: 20px;
            border-radius: 3px;
        }
        .floating-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background: linear-gradient(135deg, #FF6B6B 0%, #FF9F43 100%);
            color: white;
            border-radius: 20px;
            padding: 3px 10px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(255, 107, 107, 0.3);
            z-index: 2;
        }
        .section-title {
            font-size: 22px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        .section-title i {
            font-size: 20px;
            margin-right: 10px;
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(55, 81, 255, 0.1);
            color: #3751FF;
        }
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 6px;
            position: relative;
            display: flex;
            align-items: center;
        }
        .page-title i {
            font-size: 24px;
            color: #3751FF;
            margin-right: 10px;
        }
        .page-subtitle {
            font-size: 15px;
            color: #6b7280;
            margin-bottom: 20px;
        }
        .price-badge {
            display: inline-block;
            background-color: #3751FF;
            color: white;
            font-weight: bold;
            padding: 6px 12px;
            border-radius: 20px;
            margin-left: 10px;
            font-size: 16px;
        }
        .benefits-container {
            background-color: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        .benefit-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        .benefit-item:last-child {
            margin-bottom: 0;
        }
        .benefit-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background-color: rgba(55, 81, 255, 0.1);
            color: #3751FF;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .benefit-text {
            font-size: 15px;
            color: #4b5563;
            line-height: 1.4;
        }
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            70% {
                transform: scale(1.3);
                opacity: 0;
            }
            100% {
                transform: scale(1.3);
                opacity: 0;
            }
        }
        /* 添加星星粒子背景 */
        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
        .star {
            position: absolute;
            background-color: white;
            border-radius: 50%;
            opacity: 0.4;
            animation: twinkle 3s infinite;
        }
        @keyframes twinkle {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">会员介绍</div>
        <div class="nav-restore">恢复购买</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="member-container">
            <!-- 星星粒子效果 -->
            <div class="stars"></div>
            
            <!-- 功能滑动区域 -->
            <div class="section-title">
                <i class="fas fa-star"></i>Pro专属功能
            </div>
            <div class="feature-slider-container">
                <div class="feature-slider" id="featureSlider">
                    <!-- 功能1：无广告体验 -->
                    <div class="feature-card" style="--feature-color-rgb: 255, 59, 48">
                        <div class="floating-badge">热门</div>
                        <div class="feature-icon-wrapper" style="background-color: #FF3B30;">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="feature-title">永久无广告</div>
                        <div class="feature-desc">专属会员体验，浏览网页再无广告打扰，让您的阅读体验更加纯净</div>
                    </div>
                    
                    <!-- 功能2：手动标记 -->
                    <div class="feature-card" style="--feature-color-rgb: 255, 149, 0">
                        <div class="feature-icon-wrapper" style="background-color: #FF9500;">
                            <i class="fas fa-highlighter"></i>
                        </div>
                        <div class="feature-title">手动标记</div>
                        <div class="feature-desc">轻松标记网页元素，一键去除不想看到的内容，随心定制浏览体验</div>
                    </div>
                    
                    <!-- 功能3：双引擎翻译 -->
                    <div class="feature-card" style="--feature-color-rgb: 90, 200, 250">
                        <div class="feature-icon-wrapper" style="background-color: #5AC8FA;">
                            <i class="fas fa-language"></i>
                        </div>
                        <div class="feature-title">双引擎翻译</div>
                        <div class="feature-desc">网页一键翻译，支持多种翻译引擎和语言，畅游全球网站无障碍</div>
                    </div>
                    
                    <!-- 功能4：油猴增强 -->
                    <div class="feature-card" style="--feature-color-rgb: 88, 86, 214">
                        <div class="floating-badge">高级</div>
                        <div class="feature-icon-wrapper" style="background-color: #5856D6;">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="feature-title">油猴增强</div>
                        <div class="feature-desc">支持脚本更新与黑白名单等高级功能，让网页功能更丰富、使用更便捷</div>
                    </div>
                    
                    <!-- 功能5：自动翻页 -->
                    <div class="feature-card" style="--feature-color-rgb: 175, 82, 222">
                        <div class="feature-icon-wrapper" style="background-color: #AF52DE;">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="feature-title">自动翻页</div>
                        <div class="feature-desc">网页自动翻页和智能拼页等高级特性，阅读长文章不再需要频繁点击</div>
                    </div>
                    
                    <!-- 功能6：iCloud同步 -->
                    <div class="feature-card" style="--feature-color-rgb: 100, 210, 255">
                        <div class="feature-icon-wrapper" style="background-color: #64D2FF;">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="feature-title">iCloud同步</div>
                        <div class="feature-desc">支持书签、脚本和标记等数据备份，换设备也不丢失您的个性化设置</div>
                    </div>
                    
                    <!-- 功能7：多平台支持 -->
                    <div class="feature-card" style="--feature-color-rgb: 52, 199, 89">
                        <div class="feature-icon-wrapper" style="background-color: #34C759;">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="feature-title">多平台支持</div>
                        <div class="feature-desc">支持iPhone/iPad多设备同时使用，一次购买多处受益</div>
                    </div>
                </div>
                
                <!-- 滑动指示器 -->
                <div class="feature-indicator" id="featureIndicator">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                </div>
            </div>
            
            <!-- 会员权益 -->
            <div class="section-title">
                <i class="fas fa-gift"></i>会员特权
            </div>
            <div class="benefits-container">
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-infinity"></i>
                    </div>
                    <div class="benefit-text">一次付费，永久使用，终身享受所有Pro功能</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="benefit-text">持续更新，获得最新功能和体验改进</div>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="benefit-text">优先客服支持，问题快速响应解决</div>
                </div>
            </div>
            
            <!-- 购买按钮 -->
            <button class="btn-primary">购买会员 ¥38</button>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 创建星星背景
            createStars();
            
            // 处理滑动指示器
            const slider = document.getElementById('featureSlider');
            const indicator = document.getElementById('featureIndicator');
            const dots = indicator.querySelectorAll('.indicator-dot');
            const cards = slider.querySelectorAll('.feature-card');
            
            let currentIndex = 0;
            const cardWidth = cards[0].offsetWidth + 20; // 卡片宽度 + 间距
            
            // 监听滚动事件
            slider.addEventListener('scroll', function() {
                currentIndex = Math.round(slider.scrollLeft / cardWidth);
                updateIndicator();
            });
            
            function updateIndicator() {
                dots.forEach((dot, index) => {
                    if (index === currentIndex) {
                        dot.classList.add('active');
                    } else {
                        dot.classList.remove('active');
                    }
                });
            }
            
            // 点击指示器切换卡片
            dots.forEach((dot, index) => {
                dot.addEventListener('click', function() {
                    currentIndex = index;
                    slider.scrollTo({
                        left: cardWidth * index,
                        behavior: 'smooth'
                    });
                    updateIndicator();
                });
            });
        });
        
        // 创建星星效果
        function createStars() {
            const starsContainer = document.querySelector('.stars');
            const starsCount = 30;
            
            for (let i = 0; i < starsCount; i++) {
                const star = document.createElement('div');
                star.classList.add('star');
                
                // 随机位置
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                
                // 随机大小
                const size = Math.random() * 2 + 1;
                
                // 随机动画延迟
                const delay = Math.random() * 3;
                
                star.style.left = `${x}%`;
                star.style.top = `${y}%`;
                star.style.width = `${size}px`;
                star.style.height = `${size}px`;
                star.style.animationDelay = `${delay}s`;
                
                starsContainer.appendChild(star);
            }
        }
    </script>
</body>
</html> 