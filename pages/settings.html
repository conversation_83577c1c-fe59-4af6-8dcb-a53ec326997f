<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
            transition: background-color 0.2s ease;
        }
        .settings-item:hover {
            background-color: rgba(0, 0, 0, 0.01);
        }
        .settings-item:active {
            background-color: rgba(0, 0, 0, 0.02);
        }
        .settings-item:last-child {
            border-bottom: none;
        }
        
        /* 设置图标样式优化 */
        .settings-icon {
            width: 32px;
            height: 32px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
            color: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }
        .settings-item:hover .settings-icon {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
        }
        
        /* 开关样式优化 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e4e4e4;
            transition: .4s;
            border-radius: 34px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        input:checked + .toggle-slider {
            background-color: #007aff;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        /* Pro徽章优化 */
        .pro-badge {
            background-color: #007aff;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
            box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
        }
        
        /* Pro版本入口优化 */
        .pro-item {
            padding: 16px;
            margin: 0 16px 16px;
            border-radius: 14px;
            background: linear-gradient(135deg, #3a7bd5, #00d2ff);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
            position: relative;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .pro-item:active {
            transform: scale(0.98);
        }
        .pro-icon {
            width: 38px;
            height: 38px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
            color: #3a7bd5;
            font-size: 18px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .pro-info {
            flex: 1;
        }
        .pro-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        .pro-desc {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
        }
        .pro-arrow {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }
        .pro-shine {
            position: absolute;
            top: 0;
            left: -100%;
            width: 50px;
            height: 100%;
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            animation: shine 3s infinite;
        }
        @keyframes shine {
            0% { left: -100px; }
            20% { left: 150%; }
            100% { left: 150%; }
        }
        
        /* 导航栏优化 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        /* 内容区域优化 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        /* 分组标题优化 */
        .group-title {
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #8e8e93;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 4px;
        }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">设置</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container">
            <!-- Pro版本 -->
            <div class="pro-item">
                <div class="pro-shine"></div>
                <div class="flex items-center">
                    <div class="pro-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="pro-info">
                        <div class="pro-title">Pro高级版</div>
                        <div class="pro-desc">解锁全部高级功能</div>
                    </div>
                </div>
                <div class="pro-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
            
            <!-- 基础设置 -->
            <div class="px-4 py-2">
                <h2 class="group-title">基础设置</h2>
                <div class="settings-group">
                    <!-- 通用设置 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #5856D6;">
                                <i class="fas fa-sliders-h"></i>
                            </div>
                            <div>通用设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 主页设置 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF9500;">
                                <i class="fas fa-home"></i>
                            </div>
                            <div>主页设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 网页设置 -->
                    <div class="settings-item" onclick="window.location.href='web_settings.html'">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #34C759;">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div>网页设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <!-- 搜索设置 -->
                    <div class="settings-item" onclick="window.location.href='search_settings.html'">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #4285F4;">
                                <i class="fas fa-search"></i>
                            </div>
                            <div>搜索设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 进阶功能 -->
            <div class="px-4 py-2">
                <h2 class="group-title">进阶功能</h2>
                <div class="settings-group">
                    <!-- 翻译设置 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #5AC8FA;">
                                <i class="fas fa-language"></i>
                            </div>
                            <div>翻译设置</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 自动翻页 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #AF52DE;">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>自动翻页</div>
                            <span class="pro-badge">Pro</span>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- iCloud同步 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #64D2FF;">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div>iCloud同步</div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 隐私与安全 -->
            <div class="px-4 py-2">
                <h2 class="group-title">隐私与安全</h2>
                <div class="settings-group">
                    <!-- 广告过滤 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF2D55;">
                                <i class="fas fa-ban"></i>
                            </div>
                            <div>广告过滤</div>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 拦截跳转 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF9500;">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>拦截跳转</div>
                            <span class="pro-badge">Pro</span>
                        </div>
                        <div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 清理数据 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF3B30;">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                            <div>清理数据</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 支持与帮助 -->
            <div class="px-4 py-2">
                <h2 class="group-title">支持与帮助</h2>
                <div class="settings-group">
                    <!-- 使用帮助 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #30B0C7;">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div>使用帮助</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 评价应用 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF9500;">
                                <i class="fas fa-star"></i>
                            </div>
                            <div>评价应用</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 分享给朋友 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #34C759;">
                                <i class="fas fa-share-alt"></i>
                            </div>
                            <div>分享给朋友</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 关于 -->
            <div class="px-4 py-2">
                <h2 class="group-title">关于</h2>
                <div class="settings-group">
                    <!-- 隐私协议 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #8E8E93;">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div>隐私协议</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 关于我们 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #007AFF;">
                                <i class="fas fa-info"></i>
                            </div>
                            <div>关于我们</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <div class="text-gray-500 mr-2">版本 1.0.0</div>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 下载管理 -->
            <div class="px-4 py-2">
                <h2 class="group-title">下载管理</h2>
                <div class="settings-group">
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background: linear-gradient(135deg, #4CAF50, #8BC34A);">
                                <i class="fas fa-history"></i>
                            </div>
                            <span>历史记录</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background: linear-gradient(135deg, #2196F3, #03A9F4);">
                                <i class="fas fa-download"></i>
                            </div>
                            <span>下载管理</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background: linear-gradient(135deg, #9C27B0, #E91E63);">
                                <i class="fas fa-bookmark"></i>
                            </div>
                            <span>书签</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 