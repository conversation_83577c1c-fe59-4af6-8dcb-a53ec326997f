<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 网页设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .settings-item {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
            transition: background-color 0.2s ease;
        }
        .settings-item:hover {
            background-color: rgba(0, 0, 0, 0.01);
        }
        .settings-item:active {
            background-color: rgba(0, 0, 0, 0.02);
        }
        .settings-item:last-child {
            border-bottom: none;
        }
        
        /* 设置图标样式优化 */
        .settings-icon {
            width: 32px;
            height: 32px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 14px;
            color: white;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }
        .settings-item:hover .settings-icon {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
        }
        
        /* 开关样式优化 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e4e4e4;
            transition: .4s;
            border-radius: 34px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 24px;
            width: 24px;
            left: 3px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        input:checked + .toggle-slider {
            background-color: #007aff;
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
        
        /* Pro徽章样式 */
        .pro-badge {
            background-color: #007aff;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
            box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
        }
        
        /* 导航栏样式 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        /* 内容区域样式 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        /* 分组标题样式 */
        .group-title {
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #8e8e93;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            padding-left: 4px;
        }
        
        /* 选择器样式 */
        .selected-value {
            color: #8e8e93;
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">网页设置</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container">
            <!-- 基本网页设置 -->
            <div class="px-4 py-2">
                <h2 class="group-title">基本设置</h2>
                <div class="settings-group">
                    <!-- 搜索引擎 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #5856D6;">
                                <i class="fas fa-search"></i>
                            </div>
                            <div>搜索引擎</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <span class="selected-value mr-2">百度</span>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 浏览器标识 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF9500;">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <div>浏览器标识</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <span class="selected-value mr-2">系统默认</span>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    
                    <!-- 弹出式窗口打开方式 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #34C759;">
                                <i class="fas fa-external-link-alt"></i>
                            </div>
                            <div>弹出式窗口打开方式</div>
                        </div>
                        <div class="flex items-center text-gray-500">
                            <span class="selected-value mr-2">新标签页</span>
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 页面交互设置 -->
            <div class="px-4 py-2">
                <h2 class="group-title">页面交互</h2>
                <div class="settings-group">
                    <!-- 开启下拉刷新 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #007AFF;">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div>开启下拉刷新</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <!-- 启动时恢复窗口 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF2D55;">
                                <i class="fas fa-history"></i>
                            </div>
                            <div>启动时恢复窗口</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 视频和图片设置 -->
            <div class="px-4 py-2">
                <h2 class="group-title">视频和图片</h2>
                <div class="settings-group">
                    <!-- 开启视频模式 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #AF52DE;">
                                <i class="fas fa-film"></i>
                            </div>
                            <div>开启视频模式</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <!-- 开启长按失败视频 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #FF3B30;">
                                <i class="fas fa-hand-pointer"></i>
                            </div>
                            <div>开启长按失败视频</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <!-- 开启长按识别图片 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #5AC8FA;">
                                <i class="fas fa-image"></i>
                            </div>
                            <div>开启长按识别图片</div>
                            <span class="pro-badge">Pro</span>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <!-- 开启长按预览图片 -->
                    <div class="settings-item">
                        <div class="flex items-center">
                            <div class="settings-icon" style="background-color: #4CD964;">
                                <i class="fas fa-search-plus"></i>
                            </div>
                            <div>开启长按预览图片</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
    <script>
        // 返回按钮功能
        document.querySelector('.nav-back').addEventListener('click', function() {
            window.history.back();
        });
        
        // 切换开关的功能
        document.querySelectorAll('.toggle-switch input').forEach(function(toggle) {
            toggle.addEventListener('change', function() {
                // 这里可以添加保存设置的逻辑
                console.log('设置已更改:', this.checked);
                
                // Pro功能检查示例
                const settingItem = this.closest('.settings-item');
                const isProFeature = settingItem.querySelector('.pro-badge') !== null;
                
                if (isProFeature && this.checked) {
                    // 如果是Pro功能并且用户尝试开启，检查用户是否为Pro会员
                    const isPro = localStorage.getItem('isMember') === 'true';
                    if (!isPro) {
                        // 如果不是Pro会员，显示提示并恢复开关状态
                        alert('此功能仅对Pro会员开放，请升级会员以使用此功能。');
                        this.checked = false;
                    }
                }
            });
        });
        
        // 选择项的点击功能
        const selectableItems = document.querySelectorAll('.settings-item:has(.selected-value)');
        selectableItems.forEach(item => {
            item.addEventListener('click', function() {
                const settingName = this.querySelector('.flex.items-center div:not(.settings-icon)').textContent;
                console.log('点击了设置项:', settingName);
                
                // 这里可以添加弹出选择器的逻辑
                // 示例：根据设置类型显示不同的选择器
                switch(settingName) {
                    case '搜索引擎':
                        // 显示搜索引擎选择器
                        alert('搜索引擎选择器（演示）');
                        break;
                    case '浏览器标识':
                        // 显示浏览器标识选择器
                        alert('浏览器标识选择器（演示）');
                        break;
                    case '弹出式窗口打开方式':
                        // 显示窗口打开方式选择器
                        alert('窗口打开方式选择器（演示）');
                        break;
                }
            });
        });
    </script>
</body>
</html> 