<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 工具栏排序设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            height: 100vh;
            overflow: hidden;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* 页面过渡动画 */
        .settings-container {
            padding-top: 10px;
            padding-bottom: 30px;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 设置组样式优化 */
        .settings-group {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.25s ease;
            overflow: hidden;
        }
        .settings-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        /* 导航栏样式 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 12px 16px;
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 10;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        .nav-back {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }
        .nav-back:active {
            background-color: rgba(0, 122, 255, 0.1);
        }
        .nav-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        /* 内容区域样式 */
        .content-area {
            position: absolute;
            top: 60px; /* 状态栏高度 */
            left: 0;
            right: 0;
            bottom: 0;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 30px;
            background-color: #f8f9fa;
        }
        
        /* 工具栏排序选项 */
        .toolbar-option {
            border-radius: 14px;
            background-color: #fff;
            margin-bottom: 16px;
            overflow: hidden;
            position: relative;
            transition: all 0.25s ease;
        }
        
        .toolbar-option:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .toolbar-option.selected {
            box-shadow: 0 0 0 2px #007aff;
        }
        
        .toolbar-preview {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid rgba(0, 0, 0, 0.035);
        }
        
        .toolbar-button {
            width: 18%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .button-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background-color: #f2f2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .button-label {
            font-size: 11px;
            color: #333;
        }
        
        .toolbar-info {
            padding: 14px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .toolbar-name {
            font-weight: 500;
        }
        
        .radio-button {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            border: 2px solid #d1d1d6;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        
        .radio-button.selected {
            border-color: #007aff;
        }
        
        .radio-button.selected::after {
            content: "";
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #007aff;
        }
        
        /* 说明文本 */
        .info-text {
            padding: 0 4px;
            margin-bottom: 16px;
            color: #8e8e93;
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-gray-100 w-full h-full">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="nav-back" onclick="history.back()">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div class="nav-title">工具栏排序设置</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area">
        <div class="settings-container px-4">
            <p class="info-text">选择您喜欢的工具栏按钮排序方式</p>
            
            <!-- 排序选项1 -->
            <div class="toolbar-option selected" data-option="1">
                <div class="toolbar-preview">
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="button-label">后退</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="button-label">前进</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="button-label">新建</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="button-label">标签页</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-ellipsis"></i>
                        </div>
                        <div class="button-label">功能</div>
                    </div>
                </div>
                <div class="toolbar-info">
                    <div class="toolbar-name">默认排序</div>
                    <div class="radio-button selected"></div>
                </div>
            </div>
            
            <!-- 排序选项2 -->
            <div class="toolbar-option" data-option="2">
                <div class="toolbar-preview">
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="button-label">后退</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="button-label">前进</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-ellipsis"></i>
                        </div>
                        <div class="button-label">功能</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="button-label">标签页</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="button-label">主页</div>
                    </div>
                </div>
                <div class="toolbar-info">
                    <div class="toolbar-name">功能优先</div>
                    <div class="radio-button"></div>
                </div>
            </div>
            
            <!-- 排序选项3 -->
            <div class="toolbar-option" data-option="3">
                <div class="toolbar-preview">
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="button-label">后退</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="button-label">前进</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="button-label">主页</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="button-label">标签页</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-ellipsis"></i>
                        </div>
                        <div class="button-label">功能</div>
                    </div>
                </div>
                <div class="toolbar-info">
                    <div class="toolbar-name">主页中间</div>
                    <div class="radio-button"></div>
                </div>
            </div>
            
            <!-- 排序选项4 -->
            <div class="toolbar-option" data-option="4">
                <div class="toolbar-preview">
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="button-label">后退</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="button-label">前进</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="button-label">标签页</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-ellipsis"></i>
                        </div>
                        <div class="button-label">功能</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="button-label">主页</div>
                    </div>
                </div>
                <div class="toolbar-info">
                    <div class="toolbar-name">主页最右</div>
                    <div class="radio-button"></div>
                </div>
            </div>
            
            <!-- 排序选项5 -->
            <div class="toolbar-option" data-option="5">
                <div class="toolbar-preview">
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="button-label">后退</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="button-label">前进</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="button-label">新建</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-ellipsis"></i>
                        </div>
                        <div class="button-label">功能</div>
                    </div>
                    <div class="toolbar-button">
                        <div class="button-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="button-label">标签页</div>
                    </div>
                </div>
                <div class="toolbar-info">
                    <div class="toolbar-name">新建优先</div>
                    <div class="radio-button"></div>
                </div>
            </div>
            
            <!-- 提示信息 -->
            <div class="bg-blue-50 p-4 rounded-xl mt-4 mb-8">
                <div class="flex">
                    <div class="text-blue-500 mr-3">
                        <i class="fas fa-info-circle text-lg"></i>
                    </div>
                    <div class="text-sm text-blue-800">
                        <p>点击选择您喜欢的工具栏排序方式，所选排序将在应用重启后生效。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 选项切换功能
        document.querySelectorAll('.toolbar-option').forEach(option => {
            option.addEventListener('click', () => {
                // 移除所有活跃状态
                document.querySelectorAll('.toolbar-option').forEach(opt => {
                    opt.classList.remove('selected');
                    opt.querySelector('.radio-button').classList.remove('selected');
                });
                
                // 添加当前选项活跃状态
                option.classList.add('selected');
                option.querySelector('.radio-button').classList.add('selected');
                
                // 可以在这里添加保存选择的逻辑
                const optionValue = option.getAttribute('data-option');
                console.log('Selected option:', optionValue);
                
                // 模拟选择反馈
                const feedback = document.createElement('div');
                feedback.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg text-sm';
                feedback.textContent = '已保存排序设置';
                feedback.style.opacity = '0';
                feedback.style.transition = 'opacity 0.3s ease';
                document.body.appendChild(feedback);
                
                setTimeout(() => {
                    feedback.style.opacity = '1';
                }, 10);
                
                setTimeout(() => {
                    feedback.style.opacity = '0';
                }, 2000);
                
                setTimeout(() => {
                    feedback.remove();
                }, 2300);
            });
        });
    </script>
</body>
</html> 