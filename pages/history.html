<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器 - 历史记录</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .history-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .history-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        .date-header {
            background-color: #f2f2f7;
            position: sticky;
            top: 0;
            z-index: 1;
        }
    </style>
</head>
<body class="bg-white w-full h-full overflow-hidden">
    <!-- iOS 状态栏 -->
    <div class="ios-status-bar">
        <div class="status-left">
            <span class="time">14:42</span>
        </div>
        <div class="status-right">
            <span class="signal-icon"><i class="fas fa-signal"></i></span>
            <span class="wifi-icon"><i class="fas fa-wifi"></i></span>
            <span class="battery-icon"><i class="fas fa-battery-full"></i></span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area pt-14 pb-20 h-full overflow-y-auto relative">
        <!-- 导航栏 -->
        <div class="nav-bar px-4 py-2 flex items-center justify-between">
            <div class="text-lg font-bold">历史记录</div>
            <div class="flex items-center">
                <button class="w-8 h-8 flex items-center justify-center text-gray-500">
                    <i class="fas fa-search"></i>
                </button>
                <button class="w-8 h-8 flex items-center justify-center text-gray-500">
                    <i class="fas fa-trash-alt"></i>
                </button>
                <button class="w-8 h-8 flex items-center justify-center text-gray-500">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        </div>
        
        <!-- 历史搜索 -->
        <div class="px-4 py-2">
            <div class="bg-gray-100 rounded-lg px-3 py-2 flex items-center">
                <i class="fas fa-search text-gray-500 mr-2"></i>
                <input type="text" placeholder="搜索历史记录" class="bg-transparent w-full outline-none text-sm">
            </div>
        </div>
        
        <!-- 历史列表 -->
        <div class="px-4 py-2">
            <!-- 今天 -->
            <div class="mb-4">
                <div class="date-header py-2 px-1">
                    <h2 class="text-sm font-semibold text-gray-500">今天</h2>
                </div>
                
                <div class="bg-white rounded-lg">
                    <!-- 百度 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #3388FF;">
                            <span class="text-white font-bold">百</span>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">百度一下，你就知道</div>
                            <div class="text-xs text-gray-500">www.baidu.com</div>
                            <div class="text-xs text-gray-400 mt-1">14:30</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <!-- 知乎 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #0066FF;">
                            <span class="text-white font-bold">知</span>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">知乎 - 有问题，就会有答案</div>
                            <div class="text-xs text-gray-500">www.zhihu.com</div>
                            <div class="text-xs text-gray-400 mt-1">13:45</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <!-- 微博 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #E6162D;">
                            <i class="fab fa-weibo"></i>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">微博 - 随时随地发现新鲜事</div>
                            <div class="text-xs text-gray-500">www.weibo.com</div>
                            <div class="text-xs text-gray-400 mt-1">11:20</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <!-- 哔哩哔哩 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #FB7299;">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">哔哩哔哩 - 国内知名视频弹幕网站</div>
                            <div class="text-xs text-gray-500">www.bilibili.com</div>
                            <div class="text-xs text-gray-400 mt-1">10:05</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 昨天 -->
            <div class="mb-4">
                <div class="date-header py-2 px-1">
                    <h2 class="text-sm font-semibold text-gray-500">昨天</h2>
                </div>
                
                <div class="bg-white rounded-lg">
                    <!-- 淘宝 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #FF4400;">
                            <span class="text-white font-bold">淘</span>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">淘宝网 - 淘！我喜欢</div>
                            <div class="text-xs text-gray-500">www.taobao.com</div>
                            <div class="text-xs text-gray-400 mt-1">20:15</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <!-- 京东 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #E1251B;">
                            <span class="text-white font-bold">京</span>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">京东 - 多快好省</div>
                            <div class="text-xs text-gray-500">www.jd.com</div>
                            <div class="text-xs text-gray-400 mt-1">18:30</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <!-- 微信读书 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #09BB07;">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">微信读书 - 百万好书，随时随地</div>
                            <div class="text-xs text-gray-500">weread.qq.com</div>
                            <div class="text-xs text-gray-400 mt-1">15:40</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 本周 -->
            <div class="mb-4">
                <div class="date-header py-2 px-1">
                    <h2 class="text-sm font-semibold text-gray-500">本周</h2>
                </div>
                
                <div class="bg-white rounded-lg">
                    <!-- 网易云音乐 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #C20C0C;">
                            <i class="fas fa-music"></i>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">网易云音乐 - 音乐的力量</div>
                            <div class="text-xs text-gray-500">music.163.com</div>
                            <div class="text-xs text-gray-400 mt-1">周三 19:20</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <!-- 豆瓣 -->
                    <div class="history-item py-3 flex items-center">
                        <div class="history-icon mr-3" style="background-color: #2AB557;">
                            <span class="text-white font-bold">豆</span>
                        </div>
                        <div class="flex-grow">
                            <div class="text-sm font-medium">豆瓣 - 连接一切好内容</div>
                            <div class="text-xs text-gray-500">www.douban.com</div>
                            <div class="text-xs text-gray-400 mt-1">周二 14:10</div>
                        </div>
                        <div class="text-gray-400">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-home"></i></div>
            <div class="tab-label">首页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-layer-group"></i></div>
            <div class="tab-label">标签页</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-bookmark"></i></div>
            <div class="tab-label">书签</div>
        </div>
        <div class="tab-item active">
            <div class="tab-icon"><i class="fas fa-history"></i></div>
            <div class="tab-label">历史</div>
        </div>
        <div class="tab-item">
            <div class="tab-icon"><i class="fas fa-cog"></i></div>
            <div class="tab-label">设置</div>
        </div>
    </div>
    
    <script src="../js/app.js"></script>
</body>
</html> 