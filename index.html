<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>浏览器APP原型</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .section-divider {
            margin: 40px 0;
            text-align: center;
            position: relative;
        }
        .section-divider::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            width: 100%;
            height: 1px;
            background-color: #e5e7eb;
            z-index: 1;
        }
        .section-divider h2 {
            position: relative;
            display: inline-block;
            padding: 0 20px;
            background-color: #f3f4f6;
            z-index: 2;
        }
        .ipad-container {
            width: 100%;
            height: 600px;
            background-color: #1a1a1a;
            border-radius: 20px;
            padding: 10px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            position: relative;
            overflow: hidden;
        }
        .ipad-screen {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            overflow: hidden;
            background-color: white;
        }
    </style>
</head>
<body class="bg-gray-100 p-6">
    <h1 class="text-3xl font-bold text-center mb-8">浏览器APP原型展示</h1>
    
    <!-- 手机版界面 -->
    <div class="section-divider">
        <h2 class="text-2xl font-bold bg-gray-100">手机版界面</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 主页/搜索页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">主页/搜索</h2>
            <div class="iphone-container">
                <iframe src="pages/home.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 搜索联想页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">搜索联想</h2>
            <div class="iphone-container">
                <iframe src="pages/search_suggestion.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- Google浏览页面 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">Google浏览</h2>
            <div class="iphone-container">
                <iframe src="pages/google_browsing.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 标签页管理 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">标签页管理</h2>
            <div class="iphone-container">
                <iframe src="pages/tabs.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 书签页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">书签页</h2>
            <div class="iphone-container">
                <iframe src="pages/bookmarks.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 历史记录 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">历史记录</h2>
            <div class="iphone-container">
                <iframe src="pages/history.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 设置页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">设置页</h2>
            <div class="iphone-container">
                <iframe src="pages/settings.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 下载管理页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">下载管理</h2>
            <div class="iphone-container">
                <iframe src="pages/downloads.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 隐私浏览模式 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">隐私浏览模式</h2>
            <div class="iphone-container">
                <iframe src="pages/incognito.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 会员介绍页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">会员介绍</h2>
            <div class="iphone-container">
                <iframe src="pages/member.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 网页设置页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">网页设置</h2>
            <div class="iphone-container">
                <iframe src="pages/web_settings.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>

        <!-- 工具栏设置页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">工具栏设置</h2>
            <div class="iphone-container">
                <iframe src="pages/toolbar_settings.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 工具栏排序设置页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">工具栏排序设置</h2>
            <div class="iphone-container">
                <iframe src="pages/toolbar_order_settings.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>

        <!-- 搜索设置页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">搜索设置</h2>
            <div class="iphone-container">
                <iframe src="pages/search_settings.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>

        <!-- 搜索引擎列表页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">搜索引擎列表</h2>
            <div class="iphone-container">
                <iframe src="pages/search_engines.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
    </div>

    <!-- 会员页面设计方案 -->
    <div class="section-divider mt-16">
        <h2 class="text-2xl font-bold bg-gray-100">会员页面设计方案</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 简约现代风格 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">简约现代风格</h2>
            <div class="iphone-container">
                <iframe src="pages/member_modern.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 暗黑模式风格 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">暗黑模式风格</h2>
            <div class="iphone-container">
                <iframe src="pages/member_dark.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 分类布局风格 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">分类布局风格</h2>
            <div class="iphone-container">
                <iframe src="pages/member_category.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
        
        <!-- 卡片布局风格 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">卡片布局风格</h2>
            <div class="iphone-container">
                <iframe src="pages/member_cards.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
    </div>
    
    <!-- 手机版Google浏览页面设计方案 -->
    <div class="section-divider mt-16">
        <h2 class="text-2xl font-bold bg-gray-100">Google浏览页面设计方案</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 原始设计 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">原始设计</h2>
            <div class="iphone-container">
                <iframe src="pages/google_browsing.html" frameborder="0" class="iphone-screen"></iframe>
            </div>
        </div>
    </div>
    
    <!-- iPad版界面 -->
    <div class="section-divider mt-16">
        <h2 class="text-2xl font-bold bg-gray-100">iPad版界面</h2>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- iPad主页/搜索页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">iPad主页/搜索</h2>
            <div class="ipad-container">
                <iframe src="pages/home_ipad.html" frameborder="0" class="ipad-screen"></iframe>
            </div>
        </div>
        
        <!-- iPad设置页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">iPad设置页</h2>
            <div class="ipad-container">
                <iframe src="pages/settings_ipad.html" frameborder="0" class="ipad-screen"></iframe>
            </div>
        </div>
        
        <!-- iPad会员介绍页 -->
        <div class="prototype-container">
            <h2 class="text-xl font-semibold mb-2">iPad会员介绍</h2>
            <div class="ipad-container">
                <iframe src="pages/member_ipad.html" frameborder="0" class="ipad-screen"></iframe>
            </div>
        </div>
    </div>
</body>
</html> 